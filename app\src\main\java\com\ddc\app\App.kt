package com.ddc.app

import android.app.Application
import com.ddc.app.auth.di.authModule
import com.ddc.app.core.di.coreModule
import com.ddc.app.identity.di.identityModule
import com.ddc.app.core.di.networkModule
import io.branch.referral.Branch
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.startKoin

class App : Application() {
    override fun onCreate() {
        super.onCreate()

        Branch.enableLogging()

        val branchInstance = Branch.getAutoInstance(this)

        startKoin {
            androidContext(this@App)
            modules(
                coreModule,
                authModule,
                identityModule,
                networkModule,
            )
        }
    }
}
