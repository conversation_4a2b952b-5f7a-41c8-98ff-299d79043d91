<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <aapt:attr name="android:drawable">
        <vector android:height="300dp" android:width="400dp" android:viewportHeight="300" android:viewportWidth="400">
            <group android:name="_R_G">
                <group android:name="_R_G_L_5_G" android:translateX="35.794" android:translateY="151.721" android:pivotX="0.804" android:pivotY="83.87" android:scaleX="2.5681599999999998" android:scaleY="2.5681599999999998">
                    <path android:name="_R_G_L_5_G_D_0_P_0" android:fillColor="#f6f2e8" android:fillAlpha="0" android:fillType="non<PERSON>ero" android:pathData=" M10.25 0.25 C10.25,0.25 54.58,0.25 54.58,0.25 C57.35,0.25 59.58,2.49 59.58,5.25 C59.58,5.25 59.58,78.31 59.58,78.31 C59.58,81.07 57.35,83.31 54.58,83.31 C54.58,83.31 10.25,83.31 10.25,83.31 C4.73,83.31 0.25,78.84 0.25,73.31 C0.25,73.31 0.25,10.25 0.25,10.25 C0.25,4.73 4.73,0.25 10.25,0.25c "/>
                    <path android:name="_R_G_L_5_G_D_1_P_0" android:fillColor="#f6f2e8" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M109.27 83.31 C109.27,83.31 64.93,83.31 64.93,83.31 C62.17,83.31 59.93,81.07 59.93,78.31 C59.93,78.31 59.93,5.25 59.93,5.25 C59.93,2.49 62.17,0.25 64.93,0.25 C64.93,0.25 109.27,0.25 109.27,0.25 C114.79,0.25 119.27,4.73 119.27,10.25 C119.27,10.25 119.27,73.31 119.27,73.31 C119.27,78.84 114.79,83.31 109.27,83.31c "/>
                    <path android:name="_R_G_L_5_G_D_2_P_0" android:strokeColor="#6b6b6b" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1" android:strokeAlpha="0" android:pathData=" M12.3 13.9 C12.3,13.9 49.47,13.9 49.47,13.9 "/>
                    <path android:name="_R_G_L_5_G_D_3_P_0" android:strokeColor="#6b6b6b" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1" android:strokeAlpha="0" android:pathData=" M12.3 19.18 C12.3,19.18 49.47,19.18 49.47,19.18 "/>
                    <path android:name="_R_G_L_5_G_D_4_P_0" android:strokeColor="#6b6b6b" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1" android:strokeAlpha="0" android:pathData=" M12.3 24.47 C12.3,24.47 49.47,24.47 49.47,24.47 "/>
                    <path android:name="_R_G_L_5_G_D_5_P_0" android:strokeColor="#6b6b6b" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1" android:strokeAlpha="0" android:pathData=" M12.3 29.76 C12.3,29.76 49.47,29.76 49.47,29.76 "/>
                    <path android:name="_R_G_L_5_G_D_6_P_0" android:strokeColor="#6b6b6b" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1" android:strokeAlpha="0" android:pathData=" M11.3 53.83 C11.3,53.83 48.47,53.83 48.47,53.83 "/>
                    <path android:name="_R_G_L_5_G_D_7_P_0" android:strokeColor="#6b6b6b" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1" android:strokeAlpha="0" android:pathData=" M11.3 59.11 C11.3,59.11 48.47,59.11 48.47,59.11 "/>
                    <path android:name="_R_G_L_5_G_D_8_P_0" android:strokeColor="#6b6b6b" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1" android:strokeAlpha="0" android:pathData=" M11.3 64.4 C11.3,64.4 48.47,64.4 48.47,64.4 "/>
                    <path android:name="_R_G_L_5_G_D_9_P_0" android:strokeColor="#6b6b6b" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1" android:strokeAlpha="0" android:pathData=" M11.3 69.69 C11.3,69.69 48.47,69.69 48.47,69.69 "/>
                </group>
                <group android:name="_R_G_L_4_G" android:translateX="187.5" android:translateY="110" android:pivotX="0.5" android:pivotY="19.5" android:scaleX="2.3125" android:scaleY="5.03913">
                    <path android:name="_R_G_L_4_G_D_1_P_0" android:strokeColor="#6b6b6b" android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="1" android:strokeAlpha="0" android:pathData=" M0.5 0.5 C0.5,0.5 0.5,37.67 0.5,37.67 "/>
                </group>
                <group android:name="_R_G_L_3_G" android:translateX="158.5" android:translateY="87" android:pivotX="30" android:pivotY="42" android:scaleX="2.55377" android:scaleY="2.55377">
                    <path android:name="_R_G_L_3_G_D_1_P_0" android:fillColor="#0d0b0d" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M52.58 83.31 C52.58,83.31 3.25,83.31 3.25,83.31 C1.59,83.31 0.25,81.97 0.25,80.31 C0.25,80.31 0.25,3.25 0.25,3.25 C0.25,1.59 1.59,0.25 3.25,0.25 C3.25,0.25 52.58,0.25 52.58,0.25 C56.45,0.25 59.58,3.38 59.58,7.25 C59.58,7.25 59.58,76.31 59.58,76.31 C59.58,80.18 56.45,83.31 52.58,83.31c "/>
                    <path android:name="_R_G_L_3_G_D_2_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M29.93 70.01 C31.37,70.01 32.81,70.01 34.26,70 C34.39,70 34.43,70.04 34.43,70.17 C34.42,70.86 34.42,71.54 34.43,72.23 C34.43,72.35 34.4,72.39 34.27,72.39 C33.49,72.39 32.71,72.39 31.92,72.39 C31.84,72.39 31.79,72.37 31.77,72.27 C31.56,71.54 31.09,71.06 30.36,70.88 C29.45,70.67 28.56,71.11 28.18,71.97 C28.13,72.06 28.09,72.16 28.07,72.26 C28.05,72.38 27.99,72.39 27.9,72.39 C27.12,72.39 26.35,72.39 25.57,72.39 C25.45,72.39 25.41,72.36 25.41,72.23 C25.41,71.54 25.42,70.85 25.41,70.17 C25.41,70.03 25.46,70 25.58,70 C27.03,70.01 28.48,70.01 29.93,70.01c "/>
                    <path android:name="_R_G_L_3_G_D_3_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M29.92 75.47 C28.49,75.47 27.05,75.47 25.62,75.47 C25.41,75.47 25.41,75.47 25.41,75.27 C25.41,74.61 25.41,73.95 25.41,73.29 C25.41,73.16 25.44,73.1 25.58,73.11 C26.35,73.11 27.12,73.11 27.89,73.11 C27.98,73.11 28.04,73.12 28.07,73.23 C28.24,73.81 28.59,74.24 29.15,74.5 C30.07,74.93 31.26,74.47 31.66,73.53 C31.7,73.43 31.74,73.33 31.76,73.23 C31.78,73.13 31.83,73.11 31.93,73.11 C32.71,73.11 33.49,73.11 34.26,73.11 C34.39,73.1 34.42,73.15 34.42,73.27 C34.42,73.94 34.42,74.61 34.42,75.28 C34.42,75.47 34.42,75.47 34.23,75.47 C32.79,75.47 31.36,75.47 29.92,75.47c "/>
                    <path android:name="_R_G_L_3_G_D_4_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M28.65 72.75 C28.63,72.05 29.23,71.47 29.9,71.47 C30.62,71.47 31.18,72.02 31.18,72.74 C31.19,73.45 30.63,74.02 29.92,74.02 C29.21,74.02 28.64,73.44 28.65,72.75c "/>
                    <path android:name="_R_G_L_3_G_D_6_P_0" android:fillColor="#fafafa" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M30.3 57.81 C21.78,58.1 14.23,50.96 13.89,42.41 C13.54,33.6 20.43,26.14 29.31,25.76 C38.2,25.38 45.78,32.3 45.96,41.48 C46.12,50.02 39.35,57.59 30.3,57.81c  M29.03 48.43 C29.03,48.43 29.03,42.68 29.03,42.68 C29.03,42.68 22.91,42.68 22.91,42.68 C23.02,44.63 23.42,46.48 24.15,48.26 C24.18,48.34 24.3,48.42 24.38,48.42 C25.92,48.43 27.46,48.43 29.03,48.43c  M30.95 48.43 C32.52,48.43 34.04,48.43 35.57,48.42 C35.67,48.42 35.81,48.29 35.86,48.19 C36.01,47.83 36.13,47.46 36.26,47.09 C36.73,45.66 36.99,44.19 37.07,42.66 C37.07,42.66 30.95,42.66 30.95,42.66 C30.95,42.66 30.95,48.43 30.95,48.43c  M22.92 40.76 C22.92,40.76 29.04,40.76 29.04,40.76 C29.04,40.76 29.04,35.14 29.04,35.14 C27.45,35.14 25.89,35.14 24.33,35.14 C24.27,35.15 24.17,35.24 24.14,35.31 C23.44,37.05 23.03,38.85 22.92,40.76c  M37.05 40.76 C36.99,39.06 36.51,36.9 35.88,35.38 C35.79,35.17 35.67,35.13 35.48,35.13 C34.07,35.14 32.66,35.13 31.25,35.14 C31.15,35.14 31.05,35.15 30.96,35.16 C30.96,35.16 30.96,40.76 30.96,40.76 C30.96,40.76 37.05,40.76 37.05,40.76c  M21.03 42.68 C21.03,42.68 15.84,42.68 15.84,42.68 C15.83,42.75 15.82,42.78 15.82,42.82 C15.96,44.73 16.47,46.54 17.37,48.24 C17.42,48.34 17.61,48.42 17.74,48.42 C19.11,48.43 20.48,48.43 21.85,48.43 C21.94,48.43 22.03,48.41 22.16,48.4 C21.5,46.54 21.12,44.65 21.03,42.68c  M22.17 35.12 C20.61,35.12 19.11,35.12 17.6,35.13 C17.5,35.13 17.35,35.26 17.31,35.36 C16.9,36.44 16.46,37.51 16.13,38.61 C15.93,39.29 15.89,40.02 15.77,40.75 C15.77,40.75 21.03,40.75 21.03,40.75 C21.13,38.82 21.52,36.97 22.17,35.12c  M37.82 48.39 C37.89,48.41 37.92,48.42 37.95,48.42 C39.37,48.42 40.79,48.43 42.21,48.42 C42.32,48.41 42.49,48.28 42.53,48.17 C42.9,47.27 43.3,46.38 43.57,45.45 C43.83,44.55 43.94,43.62 44.12,42.67 C44.12,42.67 38.96,42.67 38.96,42.67 C38.86,44.65 38.47,46.54 37.82,48.39c  M44.02 40.76 C44.03,40.7 44.04,40.65 44.04,40.61 C43.89,38.74 43.4,36.98 42.53,35.32 C42.48,35.22 42.29,35.14 42.17,35.14 C41.06,35.13 39.95,35.14 38.85,35.14 C38.85,35.14 37.81,35.14 37.81,35.14 C38.47,36.99 38.83,38.84 38.96,40.76 C38.96,40.76 44.02,40.76 44.02,40.76c  M26.72 28.01 C23.43,28.8 20.75,30.53 18.62,33.26 C20.03,33.26 21.35,33.25 22.68,33.27 C22.92,33.27 23.02,33.17 23.14,32.97 C23.59,32.19 24.02,31.39 24.55,30.66 C25.22,29.75 25.98,28.91 26.72,28.01c  M18.65 50.3 C20.81,53.01 23.45,54.78 26.77,55.57 C26.75,55.51 26.75,55.48 26.74,55.47 C25.24,54.01 24.02,52.34 23.06,50.48 C23.01,50.39 22.84,50.31 22.73,50.31 C21.91,50.3 21.08,50.3 20.26,50.3 C20.26,50.3 18.65,50.3 18.65,50.3c  M33.23 27.99 C33.27,28.09 33.28,28.11 33.29,28.13 C34.77,29.57 35.97,31.22 36.91,33.06 C36.96,33.16 37.15,33.25 37.27,33.25 C38.16,33.27 39.06,33.26 39.95,33.26 C39.95,33.26 41.27,33.26 41.27,33.26 C39.16,30.56 36.53,28.81 33.23,27.99c  M33.19 55.56 C36.5,54.72 39.12,52.99 41.22,50.29 C39.86,50.29 38.58,50.3 37.31,50.28 C37.07,50.28 36.96,50.36 36.85,50.57 C36.22,51.82 35.45,52.98 34.55,54.05 C34.12,54.54 33.68,55.02 33.19,55.56c  M29.04 28.58 C27.43,29.92 26.16,31.45 25.14,33.24 C25.14,33.24 29.04,33.24 29.04,33.24 C29.04,33.24 29.04,28.58 29.04,28.58c  M25.13 50.32 C26.16,52.12 27.44,53.65 29.03,54.98 C29.03,54.98 29.03,50.32 29.03,50.32 C29.03,50.32 25.13,50.32 25.13,50.32c  M30.94 33.23 C30.94,33.23 34.85,33.23 34.85,33.23 C33.8,31.45 32.55,29.9 30.94,28.58 C30.94,28.58 30.94,33.23 30.94,33.23c  M30.95 54.99 C32.54,53.63 33.82,52.11 34.84,50.31 C34.84,50.31 30.95,50.31 30.95,50.31 C30.95,50.31 30.95,54.99 30.95,54.99c "/>
                </group>
                <group android:name="_R_G_L_2_G" android:translateX="138.192" android:translateY="65.016" android:scaleX="0.8635299999999999" android:scaleY="0.8635299999999999">
                    <path android:name="_R_G_L_2_G_D_0_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M108.57 -15.93 C108.57,-15.93 108.57,-12.81 108.57,-12.81 C108.57,-12.81 106.4,-12.81 106.4,-12.81 C106.4,-12.81 106.4,-10.68 106.4,-10.68 C106.4,-10.68 108.57,-10.68 108.57,-10.68 C108.57,-10.68 108.57,-3.42 108.57,-3.42 C108.57,-0.98 109.71,0.24 111.99,0.24 C112.61,0.24 113.26,0.15 113.94,-0.04 C113.94,-0.04 113.94,-2.26 113.94,-2.26 C113.59,-2.18 113.25,-2.14 112.9,-2.14 C112.39,-2.14 112.03,-2.25 111.84,-2.47 C111.64,-2.69 111.54,-3.04 111.54,-3.53 C111.54,-3.53 111.54,-10.68 111.54,-10.68 C111.54,-10.68 113.87,-10.68 113.87,-10.68 C113.87,-10.68 113.87,-12.81 113.87,-12.81 C113.87,-12.81 111.54,-12.81 111.54,-12.81 C111.54,-12.81 111.54,-15.93 111.54,-15.93 C111.54,-15.93 108.57,-15.93 108.57,-15.93c "/>
                    <path android:name="_R_G_L_2_G_D_1_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M105.35 -12.88 C105.07,-12.99 104.72,-13.05 104.3,-13.05 C103.03,-13.05 102.03,-12.49 101.32,-11.38 C101.32,-11.38 101.25,-12.81 101.25,-12.81 C101.25,-12.81 98.41,-12.81 98.41,-12.81 C98.41,-12.81 98.41,0 98.41,0 C98.41,0 101.38,0 101.38,0 C101.38,0 101.38,-8.76 101.38,-8.76 C101.85,-9.77 102.77,-10.28 104.13,-10.28 C104.54,-10.28 104.94,-10.25 105.33,-10.18 C105.33,-10.18 105.35,-12.88 105.35,-12.88c "/>
                    <path android:name="_R_G_L_2_G_D_2_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M87.45 -9.62 C88.02,-10.37 88.79,-10.74 89.76,-10.74 C90.74,-10.74 91.52,-10.36 92.1,-9.6 C92.67,-8.84 92.96,-7.82 92.96,-6.52 C92.96,-5.06 92.68,-3.95 92.12,-3.2 C91.56,-2.44 90.78,-2.06 89.78,-2.06 C88.78,-2.06 88,-2.43 87.44,-3.18 C86.88,-3.92 86.6,-4.96 86.6,-6.28 C86.6,-7.76 86.88,-8.87 87.45,-9.62c  M83.63 -6.38 C83.63,-4.38 84.19,-2.77 85.31,-1.57 C86.43,-0.36 87.92,0.24 89.78,0.24 C91,0.24 92.08,-0.03 93.01,-0.58 C93.94,-1.12 94.66,-1.89 95.16,-2.89 C95.66,-3.88 95.91,-5.01 95.91,-6.28 C95.91,-6.28 95.9,-6.95 95.9,-6.95 C95.81,-8.79 95.21,-10.27 94.1,-11.38 C92.99,-12.49 91.54,-13.05 89.76,-13.05 C88.55,-13.05 87.49,-12.78 86.56,-12.24 C85.63,-11.7 84.91,-10.92 84.4,-9.92 C83.89,-8.91 83.63,-7.78 83.63,-6.52 C83.63,-6.52 83.63,-6.38 83.63,-6.38c "/>
                    <path android:name="_R_G_L_2_G_D_3_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M77.92 -3.17 C77.39,-2.43 76.65,-2.06 75.71,-2.06 C74.49,-2.06 73.61,-2.56 73.07,-3.55 C73.07,-3.55 73.07,-9.24 73.07,-9.24 C73.6,-10.21 74.47,-10.69 75.68,-10.69 C76.66,-10.69 77.41,-10.32 77.94,-9.57 C78.46,-8.82 78.72,-7.8 78.72,-6.52 C78.72,-5.02 78.46,-3.9 77.92,-3.17c  M81.68 -6.45 C81.68,-8.52 81.22,-10.14 80.3,-11.3 C79.39,-12.47 78.13,-13.05 76.51,-13.05 C75.02,-13.05 73.83,-12.53 72.96,-11.51 C72.96,-11.51 72.84,-12.81 72.84,-12.81 C72.84,-12.81 70.11,-12.81 70.11,-12.81 C70.11,-12.81 70.11,4.93 70.11,4.93 C70.11,4.93 73.07,4.93 73.07,4.93 C73.07,4.93 73.07,-1.15 73.07,-1.15 C73.94,-0.22 75.1,0.24 76.55,0.24 C78.11,0.24 79.36,-0.35 80.29,-1.53 C81.21,-2.71 81.68,-4.29 81.68,-6.28 C81.68,-6.28 81.68,-6.45 81.68,-6.45c "/>
                    <path android:name="_R_G_L_2_G_D_4_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M64 -2.35 C63.59,-2.06 63,-1.92 62.22,-1.92 C61.41,-1.92 60.77,-2.1 60.28,-2.47 C59.79,-2.84 59.53,-3.36 59.49,-4.04 C59.49,-4.04 56.6,-4.04 56.6,-4.04 C56.6,-3.27 56.84,-2.56 57.31,-1.89 C57.78,-1.23 58.44,-0.71 59.29,-0.33 C60.14,0.05 61.1,0.24 62.18,0.24 C63.78,0.24 65.07,-0.12 66.05,-0.83 C67.04,-1.55 67.53,-2.48 67.53,-3.63 C67.53,-4.32 67.37,-4.91 67.05,-5.39 C66.72,-5.87 66.23,-6.28 65.57,-6.62 C64.91,-6.97 63.99,-7.26 62.83,-7.51 C61.67,-7.76 60.89,-8.01 60.51,-8.25 C60.13,-8.5 59.94,-8.84 59.94,-9.28 C59.94,-9.77 60.14,-10.16 60.55,-10.45 C60.96,-10.75 61.5,-10.89 62.15,-10.89 C62.85,-10.89 63.41,-10.71 63.83,-10.34 C64.25,-9.98 64.47,-9.52 64.47,-8.96 C64.47,-8.96 67.43,-8.96 67.43,-8.96 C67.43,-10.17 66.94,-11.15 65.97,-11.91 C65,-12.67 63.73,-13.05 62.15,-13.05 C60.67,-13.05 59.44,-12.68 58.48,-11.94 C57.52,-11.19 57.04,-10.27 57.04,-9.16 C57.04,-7.85 57.78,-6.84 59.27,-6.14 C59.94,-5.83 60.78,-5.57 61.79,-5.36 C62.8,-5.16 63.52,-4.92 63.96,-4.65 C64.39,-4.38 64.61,-3.99 64.61,-3.48 C64.61,-3.01 64.41,-2.64 64,-2.35c "/>
                    <path android:name="_R_G_L_2_G_D_5_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M51.1 -2.35 C50.69,-2.06 50.1,-1.92 49.32,-1.92 C48.51,-1.92 47.86,-2.1 47.38,-2.47 C46.89,-2.84 46.62,-3.36 46.58,-4.04 C46.58,-4.04 43.7,-4.04 43.7,-4.04 C43.7,-3.27 43.94,-2.56 44.41,-1.89 C44.88,-1.23 45.54,-0.71 46.39,-0.33 C47.23,0.05 48.2,0.24 49.28,0.24 C50.88,0.24 52.16,-0.12 53.15,-0.83 C54.13,-1.55 54.63,-2.48 54.63,-3.63 C54.63,-4.32 54.47,-4.91 54.14,-5.39 C53.82,-5.87 53.33,-6.28 52.67,-6.62 C52,-6.97 51.09,-7.26 49.93,-7.51 C48.76,-7.76 47.99,-8.01 47.61,-8.25 C47.22,-8.5 47.03,-8.84 47.03,-9.28 C47.03,-9.77 47.24,-10.16 47.65,-10.45 C48.06,-10.75 48.59,-10.89 49.24,-10.89 C49.94,-10.89 50.51,-10.71 50.93,-10.34 C51.35,-9.98 51.56,-9.52 51.56,-8.96 C51.56,-8.96 54.53,-8.96 54.53,-8.96 C54.53,-10.17 54.04,-11.15 53.07,-11.91 C52.1,-12.67 50.82,-13.05 49.24,-13.05 C47.76,-13.05 46.54,-12.68 45.58,-11.94 C44.62,-11.19 44.14,-10.27 44.14,-9.16 C44.14,-7.85 44.88,-6.84 46.36,-6.14 C47.04,-5.83 47.88,-5.57 48.89,-5.36 C49.9,-5.16 50.62,-4.92 51.06,-4.65 C51.49,-4.38 51.71,-3.99 51.71,-3.48 C51.71,-3.01 51.51,-2.64 51.1,-2.35c "/>
                    <path android:name="_R_G_L_2_G_D_6_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M33.89 -2.56 C33.52,-2.89 33.33,-3.33 33.33,-3.88 C33.33,-4.56 33.6,-5.09 34.16,-5.46 C34.71,-5.83 35.53,-6.01 36.63,-6.01 C36.63,-6.01 38.23,-6.01 38.23,-6.01 C38.23,-6.01 38.23,-3.6 38.23,-3.6 C37.97,-3.13 37.58,-2.76 37.07,-2.49 C36.55,-2.21 36,-2.07 35.41,-2.07 C34.78,-2.07 34.27,-2.24 33.89,-2.56c  M41.7 0 C41.7,0 41.7,-0.2 41.7,-0.2 C41.36,-0.89 41.2,-1.81 41.2,-2.96 C41.2,-2.96 41.2,-8.74 41.2,-8.74 C41.18,-10.1 40.7,-11.16 39.78,-11.92 C38.86,-12.67 37.63,-13.05 36.08,-13.05 C35.07,-13.05 34.15,-12.87 33.32,-12.53 C32.49,-12.18 31.84,-11.7 31.36,-11.09 C30.88,-10.48 30.64,-9.82 30.64,-9.12 C30.64,-9.12 33.61,-9.12 33.61,-9.12 C33.61,-9.62 33.82,-10.04 34.25,-10.37 C34.68,-10.69 35.24,-10.86 35.91,-10.86 C36.69,-10.86 37.28,-10.66 37.66,-10.26 C38.04,-9.86 38.23,-9.33 38.23,-8.67 C38.23,-8.67 38.23,-7.83 38.23,-7.83 C38.23,-7.83 36.41,-7.83 36.41,-7.83 C34.47,-7.83 32.97,-7.46 31.93,-6.73 C30.88,-6 30.36,-4.95 30.36,-3.59 C30.36,-2.51 30.78,-1.6 31.62,-0.86 C32.45,-0.13 33.54,0.24 34.86,0.24 C36.23,0.24 37.39,-0.24 38.33,-1.2 C38.43,-0.64 38.54,-0.24 38.67,0 C38.67,0 41.7,0 41.7,0c "/>
                    <path android:name="_R_G_L_2_G_D_7_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M18.31 -14.82 C18.31,-14.82 22.11,-14.82 22.11,-14.82 C23.13,-14.81 23.94,-14.52 24.52,-13.97 C25.11,-13.41 25.4,-12.66 25.4,-11.73 C25.4,-10.79 25.11,-10.06 24.54,-9.56 C23.96,-9.06 23.12,-8.81 22.02,-8.81 C22.02,-8.81 18.31,-8.81 18.31,-8.81 C18.31,-8.81 18.31,-14.82 18.31,-14.82c  M21.99 -6.39 C24.04,-6.39 25.65,-6.86 26.79,-7.8 C27.93,-8.74 28.5,-10.06 28.5,-11.75 C28.5,-13.4 27.92,-14.73 26.75,-15.73 C25.58,-16.74 24.01,-17.24 22.02,-17.24 C22.02,-17.24 15.22,-17.24 15.22,-17.24 C15.22,-17.24 15.22,0 15.22,0 C15.22,0 18.31,0 18.31,0 C18.31,0 18.31,-6.39 18.31,-6.39 C18.31,-6.39 21.99,-6.39 21.99,-6.39c "/>
                    <path android:name="_R_G_L_2_G_D_8_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M8.91 -10 C9.38,-9.51 9.64,-8.78 9.71,-7.81 C9.71,-7.81 9.71,-7.6 9.71,-7.6 C9.71,-7.6 4.05,-7.6 4.05,-7.6 C4.19,-8.59 4.51,-9.36 5.01,-9.91 C5.51,-10.46 6.17,-10.74 6.98,-10.74 C7.8,-10.74 8.45,-10.49 8.91,-10c  M10.31 -0.4 C11.19,-0.82 11.9,-1.41 12.43,-2.18 C12.43,-2.18 10.83,-3.66 10.83,-3.66 C9.97,-2.59 8.87,-2.06 7.51,-2.06 C6.54,-2.06 5.74,-2.37 5.1,-2.98 C4.46,-3.6 4.09,-4.44 4,-5.49 C4,-5.49 12.63,-5.49 12.63,-5.49 C12.63,-5.49 12.63,-6.65 12.63,-6.65 C12.63,-8.69 12.14,-10.27 11.16,-11.38 C10.18,-12.49 8.79,-13.05 7,-13.05 C5.86,-13.05 4.84,-12.77 3.92,-12.21 C3,-11.65 2.28,-10.86 1.78,-9.86 C1.27,-8.85 1.01,-7.71 1.01,-6.43 C1.01,-6.43 1.01,-6.07 1.01,-6.07 C1.01,-4.16 1.6,-2.63 2.77,-1.49 C3.93,-0.34 5.46,0.24 7.34,0.24 C8.44,0.24 9.43,0.02 10.31,-0.4c "/>
                </group>
                <group android:name="_R_G_L_1_G" android:translateX="251" android:translateY="92.5" android:pivotX="15" android:pivotY="15" android:scaleX="3.12667" android:scaleY="3.12667">
                    <path android:name="_R_G_L_1_G_D_0_P_0" android:fillColor="#92011b" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M28.75 14.5 C28.75,22.37 22.37,28.75 14.5,28.75 C6.63,28.75 0.25,22.37 0.25,14.5 C0.25,6.63 6.63,0.25 14.5,0.25 C22.37,0.25 28.75,6.63 28.75,14.5c "/>
                    <path android:name="_R_G_L_1_G_D_1_P_0" android:fillColor="#BA0B18" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M24.37 14.5 C24.37,19.95 19.95,24.37 14.5,24.37 C9.05,24.37 4.63,19.95 4.63,14.5 C4.63,9.05 9.05,4.63 14.5,4.63 C19.95,4.63 24.37,9.05 24.37,14.5c "/>
                    <path android:name="_R_G_L_1_G_D_2_P_0" android:fillColor="#B32115" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M20.18 14.5 C20.18,17.64 17.64,20.18 14.5,20.18 C11.36,20.18 8.82,17.64 8.82,14.5 C8.82,11.36 11.36,8.82 14.5,8.82 C17.64,8.82 20.18,11.36 20.18,14.5c "/>
                </group>
                <group android:name="_R_G_L_0_G_T_1" android:translateX="250" android:translateY="276.75" android:scaleX="2.30154" android:scaleY="2.30154">
                    <group android:name="_R_G_L_0_G" android:translateX="-2.618" android:translateY="-94.151">
                        <path android:name="_R_G_L_0_G_D_0_P_0" android:fillColor="#92011b" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M55.65 81.82 C55.65,81.82 0.25,81.82 0.25,81.82 C0.25,81.82 0.25,11.93 0.25,11.93 C0.25,11.93 55.65,11.93 55.65,11.93 C55.65,11.93 55.65,81.82 55.65,81.82c "/>
                        <path android:name="_R_G_L_0_G_D_1_P_0" android:fillColor="#6b6b6b" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M50.65 96.54 C50.65,96.54 5.25,96.54 5.25,96.54 C2.49,96.54 0.25,94.31 0.25,91.54 C0.25,91.54 0.25,81.64 0.25,81.64 C0.25,81.64 55.65,81.64 55.65,81.64 C55.65,81.64 55.65,91.54 55.65,91.54 C55.65,94.31 53.41,96.54 50.65,96.54c "/>
                        <path android:name="_R_G_L_0_G_D_2_P_0" android:fillColor="#6b6b6b" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M5.25 0.25 C5.25,0.25 50.65,0.25 50.65,0.25 C53.41,0.25 55.65,2.49 55.65,5.25 C55.65,5.25 55.65,12.05 55.65,12.05 C55.65,12.05 0.25,12.05 0.25,12.05 C0.25,12.05 0.25,5.25 0.25,5.25 C0.25,2.49 2.49,0.25 5.25,0.25c "/>
                        <path android:name="_R_G_L_0_G_D_3_P_0" android:fillColor="#a6b2b5" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M32.03 8.59 C32.03,8.59 23.87,8.59 23.87,8.59 C22.53,8.59 21.43,7.49 21.43,6.15 C21.43,4.81 22.53,3.71 23.87,3.71 C23.87,3.71 32.03,3.71 32.03,3.71 C33.37,3.71 34.47,4.81 34.47,6.15 C34.47,7.49 33.37,8.59 32.03,8.59c "/>
                        <path android:name="_R_G_L_0_G_D_4_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M34.13 46.58 C34.19,49.77 33.15,52.57 31.13,55.02 C30.9,55.3 30.66,55.57 30.4,55.82 C30.07,56.13 29.72,56.15 29.47,55.92 C29.21,55.67 29.24,55.38 29.59,55.02 C31.33,53.2 32.5,51.06 32.83,48.56 C33.31,44.89 32.27,41.65 29.74,38.9 C29.68,38.83 29.61,38.77 29.54,38.69 C29.24,38.38 29.22,38.1 29.46,37.86 C29.71,37.6 30.11,37.59 30.38,37.91 C30.96,38.58 31.55,39.25 32.04,39.99 C33.35,41.98 34.07,44.18 34.13,46.58c "/>
                        <path android:name="_R_G_L_0_G_D_5_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M30.55 46.93 C30.43,49.58 29.54,51.59 27.95,53.31 C27.88,53.4 27.8,53.48 27.72,53.55 C27.41,53.82 27.07,53.84 26.82,53.6 C26.59,53.38 26.62,53.04 26.92,52.73 C28.49,51.12 29.34,49.2 29.36,46.95 C29.37,44.72 28.56,42.8 27.05,41.16 C27,41.11 26.95,41.06 26.91,41.01 C26.63,40.72 26.59,40.39 26.81,40.17 C27.05,39.92 27.42,39.91 27.7,40.21 C28.11,40.64 28.52,41.08 28.86,41.56 C30.06,43.26 30.59,45.16 30.55,46.93c "/>
                        <path android:name="_R_G_L_0_G_D_6_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M27.09 46.95 C27.04,48.61 26.43,50.08 25.22,51.29 C25.19,51.33 25.15,51.37 25.1,51.41 C24.8,51.66 24.49,51.67 24.25,51.43 C24.02,51.21 24.03,50.86 24.31,50.58 C24.92,49.98 25.39,49.29 25.64,48.46 C26.2,46.56 25.82,44.86 24.5,43.38 C24.41,43.28 24.3,43.19 24.22,43.08 C24.04,42.84 24.03,42.58 24.23,42.36 C24.43,42.14 24.68,42.08 24.94,42.25 C25.08,42.33 25.21,42.45 25.32,42.57 C26.48,43.78 27.07,45.23 27.09,46.95c "/>
                        <path android:name="_R_G_L_0_G_D_7_P_0" android:fillColor="#ffffff" android:fillAlpha="0" android:fillType="nonZero" android:pathData=" M23.99 47.03 C24.01,47.81 23.62,48.69 22.83,49.38 C22.66,49.53 22.36,49.61 22.15,49.58 C21.69,49.5 21.59,49 21.96,48.65 C22.32,48.3 22.63,47.94 22.75,47.42 C22.92,46.64 22.68,46.01 22.22,45.42 C22.15,45.33 22.07,45.26 21.99,45.17 C21.7,44.83 21.68,44.54 21.92,44.29 C22.15,44.06 22.52,44.08 22.82,44.35 C23.52,44.99 23.94,45.78 23.99,47.03c "/>
                    </group>
                </group>
            </group>
            <group android:name="time_group"/>
        </vector>
    </aapt:attr>
    <target android:name="_R_G_L_5_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_2_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="strokeAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_3_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="strokeAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_4_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="strokeAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_5_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="strokeAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_6_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="strokeAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_7_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="strokeAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_8_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="strokeAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_9_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="strokeAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_4_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="strokeAlpha" android:duration="1480" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="320" android:startOffset="1480" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="3780" android:startOffset="1800" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="strokeAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_D_2_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_D_3_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_D_4_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_D_6_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="scaleY" android:duration="0" android:startOffset="5980" android:valueFrom="2.55377" android:valueTo="0" android:valueType="floatType"/>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_2_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_3_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_4_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_5_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_6_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_7_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_8_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="320" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="820" android:startOffset="320" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="340" android:startOffset="1140" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="scaleY" android:duration="0" android:startOffset="5980" android:valueFrom="0.8635299999999999" android:valueTo="0" android:valueType="floatType"/>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_1_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="1972" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.528,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="518" android:startOffset="1972" android:valueFrom="0" android:valueTo="0.99776" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.528,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="750" android:startOffset="2490" android:valueFrom="0.99776" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.693,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="750" android:startOffset="3240" android:valueFrom="0" android:valueTo="0.99776" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.365,0 0.692,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="551" android:startOffset="3990" android:valueFrom="0.99776" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.447,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_1_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="1700" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.537,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="550" android:startOffset="1700" android:valueFrom="0" android:valueTo="0.99776" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.537,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="750" android:startOffset="2250" android:valueFrom="0.99776" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.699,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="760" android:startOffset="3000" android:valueFrom="0" android:valueTo="0.99776" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="487" android:startOffset="3760" android:valueFrom="0.99776" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.482,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_1_G_D_2_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="1451" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.538,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="560" android:startOffset="1451" android:valueFrom="0" android:valueTo="0.99776" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.538,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="769" android:startOffset="2011" android:valueFrom="0.99776" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="717" android:startOffset="2780" android:valueFrom="0" android:valueTo="0.99776" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="504" android:startOffset="3497" android:valueFrom="0.99776" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.515,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="3880" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="540" android:startOffset="3880" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="1160" android:startOffset="4420" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="3880" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="540" android:startOffset="3880" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="1160" android:startOffset="4420" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_2_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="3880" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="540" android:startOffset="3880" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="1160" android:startOffset="4420" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_3_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="3880" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="540" android:startOffset="3880" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="1160" android:startOffset="4420" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_4_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="3880" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="540" android:startOffset="3880" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="1160" android:startOffset="4420" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_5_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="3880" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="540" android:startOffset="3880" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="1160" android:startOffset="4420" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_6_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="3880" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="540" android:startOffset="3880" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="1160" android:startOffset="4420" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_D_7_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="fillAlpha" android:duration="3880" android:startOffset="0" android:valueFrom="0" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="540" android:startOffset="3880" android:valueFrom="0" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="1160" android:startOffset="4420" android:valueFrom="1" android:valueTo="1" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="fillAlpha" android:duration="380" android:startOffset="5580" android:valueFrom="1" android:valueTo="0" android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.333,0 0.667,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="translateXY" android:duration="4120" android:startOffset="0" android:propertyXName="translateX" android:propertyYName="translateY" android:pathData="M 250,276.75C 249.959,276.75 250,276.75 250,276.75">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.252,0 0.741,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator android:propertyName="translateXY" android:duration="820" android:startOffset="4120" android:propertyXName="translateX" android:propertyYName="translateY" android:pathData="M 250,276.75C 249.959,276.75 207.75,276.875 207.75,276.875">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.252,0 0.741,1 1.0,1.0"/>
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="time_group">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator android:propertyName="translateX" android:duration="6000" android:startOffset="0" android:valueFrom="0" android:valueTo="1" android:valueType="floatType"/>
            </set>
        </aapt:attr>
    </target>
</animated-vector>