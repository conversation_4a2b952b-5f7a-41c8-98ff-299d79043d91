package com.ddc.app.identity.domain.repository

import com.ddc.app.identity.data.models.Applicant
import com.ddc.app.identity.data.models.ApplicantSessionData
import com.ddc.app.identity.data.models.DeviceConfigData
import com.ddc.app.identity.data.models.IdentityState
import com.ddc.app.identity.data.models.ScanType
import kotlinx.coroutines.flow.StateFlow

interface IdentityCaptureRepositoryInterface {
    val identityState: StateFlow<IdentityState>
    suspend fun fetchSessionDetails(): ApplicantSessionData?
    suspend fun setApplicantDetails()
    fun getApplicantDetails(): Applicant?
    suspend fun getSessionToken(): Result<String>
    suspend fun fetchIdentitySessionDetails(): DeviceConfigData?
    fun updateScanCompleted(scanType: ScanType)
    fun clearIdentityStateData()
}