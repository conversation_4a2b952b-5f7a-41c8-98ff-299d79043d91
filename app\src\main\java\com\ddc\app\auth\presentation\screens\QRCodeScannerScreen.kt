package com.ddc.app.auth.presentation.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.ddc.app.auth.presentation.components.Scanner
import com.ddc.app.auth.presentation.viewmodels.QRCodeScannerViewModel
import com.ddc.app.auth.utils.qrcode.Analyser
import com.ddc.app.shared.constants.AppTexts
import com.ddc.app.shared.presentation.components.ErrorMessageDisplay
import com.ddc.app.shared.presentation.components.LoadingScreen
import com.ddc.app.shared.presentation.components.TryAgainAction
import com.ddc.app.shared.presentation.theme.ColorPrimary
import org.koin.androidx.compose.koinViewModel
import com.ddc.app.R

@Composable
fun QRCodeScannerScreen(
    navController: NavController,
    viewModel: QRCodeScannerViewModel = koinViewModel()
) {
    val analyserState = remember { mutableStateOf<Analyser?>(null) }
    val screenState = remember { mutableStateOf<ScreenState>(ScreenState.Scanning) }

    val authState = viewModel.authState.collectAsState()
    val errorState = viewModel.errorState.collectAsState()

    if (screenState.value == ScreenState.Scanning) {
        if (authState.value.applicantId.isNotEmpty() && authState.value.documentId.isNotEmpty()) {
            screenState.value = ScreenState.Loading
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        if (screenState.value == ScreenState.Scanning && errorState.value == null) {
            Scanner(
                modifier = Modifier.fillMaxSize(),
                viewModel = viewModel,
                onAnalyserCreated = { analyser ->
                    analyserState.value = analyser
                },
                onAuthenticationResult = { result ->
                    result.onSuccess { authenticated ->
                        if (!authenticated) {
                            screenState.value = ScreenState.Scanning
                            analyserState.value?.resetScanning()
                        }
                    }.onFailure {
                        screenState.value = ScreenState.Scanning
                        analyserState.value?.resetScanning()
                    }
                }
            )
            Box(
                modifier = Modifier
                    .size(250.dp)
                    .background(Color.Transparent)
                    .border(3.dp, ColorPrimary)
                    .align(Alignment.Center)
            )

            Column(
                modifier = Modifier
                    .background(ColorPrimary)
                    .align(Alignment.BottomCenter)
                    .padding(16.dp)
                    .fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = AppTexts.QR_CODE_HELP_TEXT,
                    color = Color.White,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(8.dp)
                )
            }
        }

        if (screenState.value == ScreenState.Loading && errorState.value == null) {
            LoadingScreen()
        }

        ErrorMessageDisplay(
            error = errorState.value,
            onDismiss = {
                viewModel.onErrorShown()
                analyserState.value?.resetScanning()
                screenState.value = ScreenState.Scanning
            },
            navController = navController,
            tryAgainAction = TryAgainAction.DoNothing,
            imageResId = R.drawable.qrcode_error
        )
    }
}

sealed class ScreenState {
    object Scanning : ScreenState()
    object Loading : ScreenState()
}