package com.ddc.app.identity.presentation.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.ddc.app.identity.presentation.components.identity.InstructionsList
import com.ddc.app.identity.presentation.components.identity.WelcomeMessage
import com.ddc.app.identity.presentation.viewmodels.IdentityCaptureViewModel
import com.ddc.app.shared.constants.AppTexts
import com.ddc.app.shared.presentation.theme.ColorPrimary
import org.koin.androidx.compose.koinViewModel

@Composable
fun IdentityCaptureScreen(
    startScanSession: () -> Unit,
    viewModel: IdentityCaptureViewModel = koinViewModel()
) {
    val authState by viewModel.authState.collectAsStateWithLifecycle()

    Column(
        verticalArrangement = Arrangement.SpaceBetween,
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(20.dp)
    ) {
        WelcomeMessage(authState)
        InstructionsList(viewModel.overviewImages())
        Box(
            contentAlignment = Alignment.BottomCenter,
            modifier = Modifier
                .fillMaxSize()
        ) {
            TextButton(
                onClick = { startScanSession() },
                modifier = Modifier
                    .background(ColorPrimary)
                    .fillMaxWidth()
            ) {
                Text(
                    text = AppTexts.START,
                    color = Color.White
                )
            }
        }
    }
}
