package com.ddc.app.identity.processors

import Processors.Processor
import android.content.Context
import com.ddc.app.IdentityActivity
import com.ddc.app.core.utils.AppLogger
import com.ddc.app.identity.domain.repository.ScanRepositoryInterface
import com.facetec.sdk.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class DocumentScanProcessor(
    sessionToken: String,
    context: Context,
    private val scanRepository: ScanRepositoryInterface
) : Processor(), FaceTecIDScanProcessor {
    private var success = false
    private var identityActivity: IdentityActivity = context as IdentityActivity

    init {
        FaceTecSessionActivity.createAndLaunchSession(identityActivity, this, sessionToken)
    }

    override fun processIDScanWhileFaceTecSDKWaits(
        idScanResult: FaceTecIDScanResult,
        idScanResultCallback: FaceTecIDScanResultCallback
    ) {
        identityActivity.setLatestIDScanResult(idScanResult)
        if (idScanResult.status != FaceTecIDScanStatus.SUCCESS) {
            idScanResultCallback.cancel();
            return;
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                scanRepository.processDocumentScanResult(
                    idScanResult,
                    identityActivity
                ) { completed, responseJSON, apiErrorMessage ->
                    if (completed && responseJSON != null) {
                        val scanResultBlob = responseJSON.data.scanResultBlob
                        success = idScanResultCallback.proceedToNextStep(scanResultBlob)
                    } else {
                        val messageToShow = apiErrorMessage ?: "Document scan failed. Please try again."
                        identityActivity.showScanError(messageToShow)
                        AppLogger.e("Document scan failed: $messageToShow")
                        idScanResultCallback.cancel()
                    }
                }
            } catch (e: Exception) {
                AppLogger.e("Error while processing document scan: ${e.message}")
                idScanResultCallback.cancel()
                return@launch
            }
        }
    }

    override fun isSuccess(): Boolean {
        return success
    }
}
