package com.ddc.app.auth.data.repositories

import com.ddc.app.auth.data.models.UserRequest
import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.core.network.ApiServiceInterface
import com.ddc.app.core.network.ErrorResponse
import com.ddc.app.core.utils.AppError
import com.ddc.app.core.utils.ErrorHandler
import com.ddc.app.shared.constants.QueryKeys
import com.google.gson.Gson
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import org.json.JSONObject
import retrofit2.Response

class AuthRepository(
    private val apiService: ApiServiceInterface,
) : AuthRepositoryInterface {

    private val _authState = MutableStateFlow(AuthState())
    override val authState: StateFlow<AuthState> = _authState

    override suspend fun updateAuthState(newState: AuthState) {
        _authState.update { newState }
    }

    override suspend fun login(): Result<Boolean> = handleApiCall(
        apiCall = { apiService.login(authToken = getAuthToken(), body = userRequest()) },
        transform = { response -> response != null }
    )

    override suspend fun authenticateUser(): Result<Boolean> = handleApiCall(
        apiCall = { apiService.login(authToken = getAuthToken(), body = userRequest()) },
        transform = { response -> response != null }
    )

    override suspend fun logout(): Result<Boolean> = handleApiCall(
        apiCall = { apiService.logout(authToken = getAuthToken()) },
        transform = { response -> response != null }
    )

    override suspend fun requestAuthToken(): Result<String?> = handleApiCall(
        apiCall = {
            apiService.getToken(
                applicantId = authState.value.applicantId,
                documentId = authState.value.documentId
            )
        },
        transform = { response ->
            updateAuthState(_authState.value.copy(authToken = response?.data.toString()))
            response?.data
        }
    )

    override suspend fun setAuthStateAuthToken(token: String) {
        updateAuthState(_authState.value.copy(authToken = token))
    }

    override fun userRequest() = UserRequest(
        applicantid = _authState.value.applicantId,
        documentid = _authState.value.documentId
    )

    override fun getUserData(): MutableMap<String, String?> {
        return mutableMapOf(
            QueryKeys.APPLICANT_ID to _authState.value.applicantId,
            QueryKeys.DOCUMENT_ID to _authState.value.documentId
        )
    }

    override fun userJsonData(): JSONObject {
        return JSONObject().apply {
            put(QueryKeys.APPLICANT_ID, _authState.value.applicantId)
            put(QueryKeys.DOCUMENT_ID, _authState.value.documentId)
        }
    }

    override fun getAuthToken(): String = "Bearer ${_authState.value.authToken}"

    private suspend fun <T, R> handleApiCall(
        apiCall: suspend () -> Response<T>,
        transform: suspend (T?) -> R
    ): Result<R> = try {
        coroutineScope {
            val response = apiCall()
            handleResponse(response, transform)
        }
    } catch (e: Exception) {
        Result.failure(ApiException(ErrorHandler.handleError(e)))
    }

    private suspend fun <T, R> handleResponse(
        response: Response<T>,
        transform: suspend (T?) -> R
    ): Result<R> {
        return if (response.isSuccessful) {
            Result.success(transform(response.body()))
        } else {
            val errorBody = response.errorBody()?.string()
            val errorResponse = try {
                Gson().fromJson(errorBody, ErrorResponse::class.java)
            } catch (e: Exception) {
                null
            }
            val errorMessage = errorResponse?.message
                ?: errorResponse?.error
                ?: "Error ${response.code()}: ${response.message()}"
            Result.failure(ApiException(AppError.ApiError(response.code(), errorMessage)))
        }
    }

    data class ApiException(val appError: AppError) : Exception(appError.toString())
}