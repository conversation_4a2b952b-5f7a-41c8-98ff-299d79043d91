package com.ddc.app.identity.di

import com.ddc.app.identity.data.repository.DocumentScanRepository
import com.ddc.app.identity.data.repository.IdentityCaptureRepository
import com.ddc.app.identity.data.repository.ScanRepository
import com.ddc.app.identity.domain.repository.DocumentScanRepositoryInterface
import com.ddc.app.identity.domain.repository.DocumentScanServiceInterface
import com.ddc.app.identity.domain.repository.IdentityCaptureRepositoryInterface
import com.ddc.app.identity.domain.repository.ScanRepositoryInterface
import com.ddc.app.identity.presentation.viewmodels.IdentityCaptureViewModel
import com.ddc.app.identity.services.DocumentScanService
import com.ddc.app.identity.services.ScanParameterService
import org.koin.core.module.Module
import org.koin.core.module.dsl.*
import org.koin.dsl.module

val identityModule: Module = module {
    single<DocumentScanServiceInterface> {
        DocumentScanService()
    }

    single<IdentityCaptureRepositoryInterface> {
        IdentityCaptureRepository(
            authRepository = get(),
            apiService = get(),
        )
    }

    single<DocumentScanRepositoryInterface> {
        DocumentScanRepository(
            apiService = get(),
            authRepository = get(),
        )
    }

    single<ScanParameterService> {
        ScanParameterService(get())
    }

    single<ScanRepositoryInterface> {
        ScanRepository(
            apiService = get(),
            authRepository = get(),
            scanParameterService = get(),
        )
    }

    viewModel {
        IdentityCaptureViewModel(
            authRepository = get(),
            identityCaptureRepository = get(),
        )
    }
}