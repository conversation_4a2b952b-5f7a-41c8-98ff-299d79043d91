package com.ddc.app.identity.domain.repository

import android.content.Context
import com.ddc.app.identity.data.models.document.ScanData
import com.ddc.app.identity.data.models.document.scan.IDScanResponse
import com.facetec.sdk.FaceTecIDScanResult

interface DocumentScanRepositoryInterface {
    suspend fun fetchDocumentScanResults(sessionId: String): <PERSON>olean?
    suspend fun uploadDocumentScan(scanData: ScanData): Boolean
    fun isSuccess(): Boolean;
    suspend fun processResult(
        result: FaceTecIDScanResult,
        context: Context,
        onComplete: (<PERSON><PERSON><PERSON>, IDScanResponse?) -> Unit
    )
}