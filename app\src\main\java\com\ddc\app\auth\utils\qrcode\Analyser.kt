package com.ddc.app.auth.utils.qrcode

import android.graphics.ImageFormat
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy

class Analyser(
    private val onQrCodeScanned: (String) -> Unit
) : ImageAnalysis.Analyzer {

    private val supportedImageFormats = listOf(
        ImageFormat.YUV_420_888,
        ImageFormat.YUV_422_888,
        ImageFormat.YUV_444_888,
    )

    private val qrCodeDecoder = Decoder()
    var isScanningActive: Boolean = true

    override fun analyze(image: ImageProxy) {
        if (!isScanningActive) {
            image.close()
            return
        }
        if (image.format in supportedImageFormats) {
            val bytes = image.planes.first().buffer.toByteArray()
            val decodedText = qrCodeDecoder.decodeQrCode(bytes, image.width, image.height)
            if (decodedText != null) {
                onQrCodeScanned(decodedText)
                isScanningActive = false
            }
            image.close()
        } else {
            image.close()
        }
    }

    fun resetScanning() {
        isScanningActive = true
    }
}