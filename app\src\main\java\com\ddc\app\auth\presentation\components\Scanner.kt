package com.ddc.app.auth.presentation.components

import android.content.Intent
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.ddc.app.IdentityActivity
import com.ddc.app.auth.presentation.viewmodels.QRCodeScannerViewModel
import com.ddc.app.auth.utils.qrcode.Analyser
import com.ddc.app.auth.utils.qrcode.rememberCameraPermission
import com.ddc.app.auth.utils.qrcode.setupCamera

@Composable
fun Scanner(
    modifier: Modifier = Modifier,
    viewModel: QRCodeScannerViewModel,
    onAnalyserCreated: (Analyser) -> Unit = {},
    onAuthenticationResult: (Result<Boolean>) -> Unit = {}
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }

    val analyser = remember {
        Analyser { code ->
            viewModel.handleScannedLink(code) { isDone ->
                if (isDone) {
                    viewModel.doAuthenticate { result ->
                        result.onSuccess { authenticated ->
                            if (authenticated) {
                                val intent = Intent(context, IdentityActivity::class.java)
                                context.startActivity(intent)
                            }
                        }
                        onAuthenticationResult(result)
                    }
                } else {
                    onAuthenticationResult(Result.success(false))
                }
            }
        }
    }

    onAnalyserCreated(analyser)

    if (rememberCameraPermission(context)) {
        Column(modifier = modifier.fillMaxSize()) {
            AndroidView(
                factory = { context ->
                    PreviewView(context).apply {
                        setupCamera(context, lifecycleOwner, cameraProviderFuture, analyser)
                    }
                },
                modifier = Modifier.weight(1f)
            )
        }
    }
}