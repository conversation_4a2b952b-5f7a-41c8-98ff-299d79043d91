<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:fillAfter="true"
    android:ordering="sequentially">
        <objectAnimator
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:propertyName="strokeWidth"
            android:duration="150"
            android:valueFrom="0.0f"
            android:valueTo="1.0f"
            android:valueType="floatType"
            android:interpolator="@android:interpolator/fast_out_slow_in"/>
        <objectAnimator
            xmlns:android="http://schemas.android.com/apk/res/android"
            android:propertyName="strokeWidth"
            android:duration="600"
            android:startDelay="150"
            android:valueFrom="1.0f"
            android:valueTo="10.0f"
            android:valueType="floatType"
            android:interpolator="@android:interpolator/fast_out_slow_in"/>

</set>