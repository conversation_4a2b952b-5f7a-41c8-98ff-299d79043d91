package com.ddc.app.identity.data.repository

import android.content.Context
import com.ddc.app.auth.data.repositories.AuthState
import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.core.constants.Constants
import com.ddc.app.core.network.ApiServiceInterface
import com.ddc.app.core.utils.AppLogger
import com.ddc.app.identity.data.models.document.ScanData
import com.ddc.app.identity.data.models.document.scan.IDScanResponse
import com.ddc.app.identity.domain.repository.DocumentScanRepositoryInterface
import com.facetec.sdk.FaceTecIDScanResult
import com.facetec.sdk.FaceTecSDK
import kotlinx.coroutines.flow.StateFlow
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import org.json.JSONObject
import retrofit2.Response

class DocumentScanRepository(
    private val apiService: ApiServiceInterface,
    private val authRepository: AuthRepositoryInterface
) : DocumentScanRepositoryInterface {
    val authState: StateFlow<AuthState> = authRepository.authState

    override suspend fun processResult(
        result: FaceTecIDScanResult,
        context: Context,
        onComplete: (Boolean, IDScanResponse?) -> Unit
    ) {
        val parameters = buildParameters(result)
        if (parameters != null) {
            try {
                val requestBody = buildRequestBody(parameters, context)
                val response: Response<IDScanResponse> = apiService.documentScan(
                    authToken = authRepository.getAuthToken(),
                    body = requestBody
                )

                if (!response.isSuccessful) {
                    AppLogger.e("Error while processing IDScan: ${response.code()}")
                    onComplete(false, null)
                    return
                }
                val scanResponse: IDScanResponse? = response.body()
                onComplete(true, scanResponse)
            } catch (e: Exception) {
                AppLogger.e("Error while processing response: ${e.message}")
                onComplete(false, null)
            }
        } else {
            onComplete(false, null)
        }
    }

    private fun buildParameters(result: FaceTecIDScanResult): JSONObject? {
        val parameters = authRepository.userJsonData()
        return try {
            parameters.put("version", Constants.VERSION)
            parameters.put("idScan", result.idScanBase64)

            val frontImagesCompressedBase64 = result.frontImagesCompressedBase64
            if (frontImagesCompressedBase64.isNotEmpty()) {
                parameters.put("idScanFrontImage", frontImagesCompressedBase64[0])
            }

            parameters
        } catch (e: Exception) {
            AppLogger.e("Error while building parameters: ${e.message}")
            null
        }
    }

    private fun buildRequestBody(parameters: JSONObject, context: Context): RequestBody {
        return RequestBody.create(
            "application/json; charset=utf-8".toMediaTypeOrNull(),
            parameters.toString()
        )
    }

    override suspend fun fetchDocumentScanResults(sessionId: String): Boolean? {
        return true
    }

    override suspend fun uploadDocumentScan(scanData: ScanData): Boolean {
        return true
    }

    override fun isSuccess(): Boolean {
        return true
    }
}
