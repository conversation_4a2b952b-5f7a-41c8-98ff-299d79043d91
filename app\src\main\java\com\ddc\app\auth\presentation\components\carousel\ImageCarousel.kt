package com.ddc.app.auth.presentation.components.carousel

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.ddc.app.shared.presentation.theme.ColorPrimary
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun ImageCarousel(images: List<Pair<Int, String>>) {
    var currentIndex by remember { mutableStateOf(0) }
    val coroutineScope = rememberCoroutineScope()
    var userInteracted by remember { mutableStateOf(false) }
    var lastDragTime by remember { mutableStateOf(0L) }

    LaunchedEffect(key1 = currentIndex) {
        userInteracted = false
        delay(3000)
        if (!userInteracted) {
            currentIndex = (currentIndex + 1) % images.size
        }
    }

    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp)
                .pointerInput(Unit) {
                    detectHorizontalDragGestures { _, dragAmount ->
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastDragTime > 500) {
                            lastDragTime = currentTime
                            userInteracted = true
                            coroutineScope.launch {
                                currentIndex = when {
                                    dragAmount > 0 -> (currentIndex - 1 + images.size) % images.size
                                    else -> (currentIndex + 1) % images.size
                                }
                            }
                        }
                    }
                }
        ) {
            Image(
                painter = painterResource(id = images[currentIndex].first),
                contentDescription = images[currentIndex].second,
                modifier = Modifier.fillMaxSize()
            )
        }
        Spacer(modifier = Modifier.height(35.dp))
        Row(horizontalArrangement = Arrangement.Center, modifier = Modifier.fillMaxWidth()) {
            images.indices.forEach { index ->
                Box(
                    modifier = Modifier
                        .size(if (index == currentIndex) 10.dp else 6.dp)
                        .clip(CircleShape)
                        .background(if (index == currentIndex) ColorPrimary else Color.Gray)
                )
                Spacer(modifier = Modifier.width(6.dp))
            }
        }
    }
}
