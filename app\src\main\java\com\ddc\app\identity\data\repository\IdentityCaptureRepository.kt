package com.ddc.app.identity.data.repository

import com.ddc.app.BuildConfig
import com.ddc.app.auth.data.repositories.AuthState
import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.core.network.ApiServiceInterface
import com.ddc.app.core.utils.AppLogger
import com.ddc.app.identity.data.models.Applicant
import com.ddc.app.identity.data.models.ApplicantSessionData
import com.ddc.app.identity.data.models.DeviceConfigData
import com.ddc.app.identity.data.models.IdentityState
import com.ddc.app.identity.data.models.ScanType
import com.ddc.app.identity.data.response.facetec.DeviceData
import com.ddc.app.identity.data.response.facetec.SessionResponse
import com.ddc.app.identity.domain.repository.IdentityCaptureRepositoryInterface
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import retrofit2.Response

class IdentityCaptureRepository(
    private val apiService: ApiServiceInterface,
    private val authRepository: AuthRepositoryInterface,
) : IdentityCaptureRepositoryInterface {

    val authState: StateFlow<AuthState> = authRepository.authState
    private val _identityState = MutableStateFlow(IdentityState())
    override var identityState: StateFlow<IdentityState> = _identityState.asStateFlow()

    override fun getApplicantDetails(): Applicant? {
        return Applicant(
            forename = authState.value.userFirstname,
            surname = authState.value.userLastname,
        )
    }

    override suspend fun getSessionToken(): Result<String> {
        return try {
            val response = apiService.getSessionToken(
                authToken = authState.value.authToken,
                body = authRepository.userRequest()
            )

            if (response.isSuccessful && response.body() != null) {
                response.body()?.data?.sessionToken?.let { sessionToken ->
                    authRepository.updateAuthState(authState.value.copy(sessionToken = sessionToken))
                    return Result.success(sessionToken)
                }
            }

            Result.failure(Exception("Failed to retrieve session token"))
        } catch (e: Exception) {
            AppLogger.e("Login request failed", e)
            Result.failure(e)
        }
    }

    override suspend fun setApplicantDetails() {
        val response: ApplicantSessionData? = fetchSessionDetails()
        response?.applicant.let { applicant ->
            authRepository.updateAuthState(
                authState.value.copy(
                    userFirstname = applicant?.forename ?: "",
                    userLastname = applicant?.surname ?: "",
                )
            )
        }
    }

    override suspend fun fetchSessionDetails(): ApplicantSessionData? {
        return try {
            val response: Response<ApplicantSessionData> = apiService.getUserSessionDetails(
                authToken = authRepository.getAuthToken()
            )
            response.body()
        } catch (e: Exception) {
            AppLogger.e("Error on api session details fetch: ${e.message}")
            null
        }
    }

    override suspend fun fetchIdentitySessionDetails(): DeviceConfigData? {
        try {
            val response: Response<SessionResponse> = apiService.getConfigKeys(
                authToken = authRepository.getAuthToken(),
                body = authRepository.userRequest()
            )
            if (response.isSuccessful && response.body() != null) {
                response.body()?.data?.let { data: DeviceData ->
                    return DeviceConfigData(
                        deviceKeyIdentifier = data.deviceKeyIdentifier,
                        productionKeyText = generateProductionKey(data),
                        encryptionKey = getEncryptionKey(data.face_map_encryption_key)
                    )
                }
            }
            return null
        } catch (e: Exception) {
            AppLogger.e("Error while fetching session details: ${e.message}")
            return null
        }
    }

    private fun generateProductionKey(data: DeviceData): String {
        val applicationId = BuildConfig.APPLICATION_ID
        return """
            appId      = $applicationId
            expiryDate = ${data.expiry_date}
            key        = ${data.productionKey}
        """.trimIndent();
    }

    fun getEncryptionKey(encryptionKey: String): String {
        return encryptionKey.replace("\\n", "\n")
    }

    override fun updateScanCompleted(scanType: ScanType) {
        _identityState.value = when (scanType) {
            ScanType.DOCUMENT -> _identityState.value.copy(documentScan = true)
            ScanType.FACE -> _identityState.value.copy(faceScan = true)
        }
    }

    override fun clearIdentityStateData() {
        _identityState.value.copy(
            documentScan = false,
            faceScan = false
        )
    }
}