package Processors;

import android.content.Context;
import android.graphics.Color;

import com.facetec.sdk.FaceTecCancelButtonCustomization;
import com.facetec.sdk.FaceTecCustomization;
import com.facetec.sdk.FaceTecSDK;
import com.facetec.sdk.FaceTecSecurityWatermarkImage;
import com.facetec.sdk.R;
import android.util.Log;

public class Config {
    public static void initializeFaceTecSDK(
            Context context,
            String productionKeyText,
            String deviceKeyIdentifier,
            String encryptionKey,
            FaceTecSDK.InitializeCallback callback
    ) {
        FaceTecSDK.initializeInProductionMode(context, productionKeyText, deviceKeyIdentifier, encryptionKey, callback);
    }

    public static FaceTecCustomization retrieveConfigurationWizardCustomization() {

        int outerBackgroundColor = Color.parseColor("#ffffff");
        int frameColor = Color.parseColor("#ffffff");
        int borderColor = Color.parseColor("#417FB2");
        int ovalColor = Color.parseColor("#417FB2");
        int dualSpinnerColor = Color.parseColor("#417FB2");
        int textColor = Color.parseColor("#417FB2");
        int buttonAndFeedbackBarColor = Color.parseColor("#417FB2");
        int buttonAndFeedbackBarTextColor = Color.parseColor("#ffffff");
        int buttonColorHighlight = Color.parseColor("#396E99");
        int buttonColorDisabled = Color.parseColor("#B9CCDE");
        int frameCornerRadius = 20;
        int cancelButtonImage = R.drawable.facetec_cancel;
        int yourAppLogoImage = R.drawable.facetec_your_app_logo;

        FaceTecCancelButtonCustomization.ButtonLocation cancelButtonLocation = FaceTecCancelButtonCustomization.ButtonLocation.TOP_LEFT;
        FaceTecSecurityWatermarkImage securityWatermarkImage = FaceTecSecurityWatermarkImage.FACETEC;
        FaceTecCustomization defaultCustomization = new FaceTecCustomization();
        defaultCustomization.getFrameCustomization().cornerRadius = frameCornerRadius;
        defaultCustomization.getFrameCustomization().backgroundColor = frameColor;
        defaultCustomization.getFrameCustomization().borderColor = borderColor;
        defaultCustomization.getOverlayCustomization().brandingImage = yourAppLogoImage;
        defaultCustomization.getOverlayCustomization().backgroundColor = outerBackgroundColor;
        defaultCustomization.getGuidanceCustomization().backgroundColors = frameColor;
        defaultCustomization.getGuidanceCustomization().foregroundColor = textColor;
        defaultCustomization.getGuidanceCustomization().buttonBackgroundNormalColor = buttonAndFeedbackBarColor;
        defaultCustomization.getGuidanceCustomization().buttonBackgroundDisabledColor = buttonColorDisabled;
        defaultCustomization.getGuidanceCustomization().buttonBackgroundHighlightColor = buttonColorHighlight;
        defaultCustomization.getGuidanceCustomization().buttonTextNormalColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getGuidanceCustomization().buttonTextDisabledColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getGuidanceCustomization().buttonTextHighlightColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getGuidanceCustomization().retryScreenImageBorderColor = borderColor;
        defaultCustomization.getGuidanceCustomization().retryScreenOvalStrokeColor = borderColor;
        defaultCustomization.getOvalCustomization().strokeColor = ovalColor;
        defaultCustomization.getOvalCustomization().progressColor1 = dualSpinnerColor;
        defaultCustomization.getOvalCustomization().progressColor2 = dualSpinnerColor;
        defaultCustomization.getFeedbackCustomization().backgroundColors = buttonAndFeedbackBarColor;
        defaultCustomization.getFeedbackCustomization().textColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getCancelButtonCustomization().customImage = cancelButtonImage;
        defaultCustomization.getCancelButtonCustomization().setLocation(cancelButtonLocation);
        defaultCustomization.getResultScreenCustomization().backgroundColors = frameColor;
        defaultCustomization.getResultScreenCustomization().foregroundColor = textColor;
        defaultCustomization.getResultScreenCustomization().activityIndicatorColor = buttonAndFeedbackBarColor;
        defaultCustomization.getResultScreenCustomization().resultAnimationBackgroundColor = buttonAndFeedbackBarColor;
        defaultCustomization.getResultScreenCustomization().resultAnimationForegroundColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getResultScreenCustomization().uploadProgressFillColor = buttonAndFeedbackBarColor;
        defaultCustomization.securityWatermarkImage = securityWatermarkImage;
        defaultCustomization.getIdScanCustomization().selectionScreenBackgroundColors = frameColor;
        defaultCustomization.getIdScanCustomization().selectionScreenForegroundColor = textColor;
        defaultCustomization.getIdScanCustomization().reviewScreenBackgroundColors = frameColor;
        defaultCustomization.getIdScanCustomization().reviewScreenForegroundColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getIdScanCustomization().reviewScreenTextBackgroundColor = buttonAndFeedbackBarColor;
        defaultCustomization.getIdScanCustomization().captureScreenForegroundColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getIdScanCustomization().captureScreenTextBackgroundColor = buttonAndFeedbackBarColor;
        defaultCustomization.getIdScanCustomization().buttonBackgroundNormalColor = buttonAndFeedbackBarColor;
        defaultCustomization.getIdScanCustomization().buttonBackgroundDisabledColor = buttonColorDisabled;
        defaultCustomization.getIdScanCustomization().buttonBackgroundHighlightColor = buttonColorHighlight;
        defaultCustomization.getIdScanCustomization().buttonTextNormalColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getIdScanCustomization().buttonTextDisabledColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getIdScanCustomization().buttonTextHighlightColor = buttonAndFeedbackBarTextColor;
        defaultCustomization.getIdScanCustomization().captureScreenBackgroundColor = frameColor;
        defaultCustomization.getIdScanCustomization().captureFrameStrokeColor = borderColor;

        return defaultCustomization;
    }

    public static FaceTecCustomization retrieveLowLightConfigurationWizardCustomization() {
        return retrieveConfigurationWizardCustomization();
    }

    public static FaceTecCustomization retrieveDynamicDimmingConfigurationWizardCustomization() {
        return retrieveConfigurationWizardCustomization();
    }

    public static FaceTecCustomization currentCustomization = retrieveConfigurationWizardCustomization();
    public static FaceTecCustomization currentLowLightCustomization = retrieveLowLightConfigurationWizardCustomization();
    public static FaceTecCustomization currentDynamicDimmingCustomization = retrieveDynamicDimmingConfigurationWizardCustomization();
}
