package com.ddc.app.identity.services

import android.content.Context
import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.core.constants.Constants
import com.ddc.app.core.utils.AppLogger
import com.facetec.sdk.FaceTecIDScanResult
import com.facetec.sdk.FaceTecSessionResult
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import org.json.JSONObject

class ScanParameterService(
    private val authRepository: AuthRepositoryInterface,
) {
    fun buildCommonParameters(): JSONObject? {
        return try {
            authRepository.userJsonData().apply {
                put("version", Constants.VERSION)
            }
        } catch (e: Exception) {
            AppLogger.e("Error while building common parameters: ${e.message}")
            null
        }
    }

    fun buildDocumentScanParameters(result: FaceTecIDScanResult): JSONObject? {
        return buildCommonParameters()?.apply {
            try {
                put("idScan", result.idScanBase64)
                result.frontImagesCompressedBase64.firstOrNull()?.let {
                    put("idScanFrontImage", it)
                }
            } catch (e: Exception) {
                AppLogger.e("Error while adding ID scan parameters: ${e.message}")
                return null
            }
        }
    }

    fun buildFaceScanParameters(result: FaceTecSessionResult): JSONObject? {
        return buildCommonParameters()?.apply {
            try {
                put("faceScan", result.faceScanBase64)
                put("auditTrailImage", result.auditTrailCompressedBase64.firstOrNull())
                put(
                    "lowQualityAuditTrailImage",
                    result.lowQualityAuditTrailCompressedBase64.firstOrNull()
                )
            } catch (e: Exception) {
                AppLogger.e("Error while adding face scan parameters: ${e.message}")
                return null
            }
        }
    }

    fun buildRequestBody(parameters: JSONObject): RequestBody {
        val jsonBody = parameters.toString()
        return RequestBody.create(
            "application/json; charset=utf-8".toMediaTypeOrNull(),
            jsonBody
        )
    }
}
