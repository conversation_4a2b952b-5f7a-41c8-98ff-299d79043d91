package com.ddc.app.identity.data.repository

import android.content.Context
import com.ddc.app.auth.data.repositories.AuthState
import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.core.network.ApiServiceInterface
import com.ddc.app.core.utils.AppLogger
import com.ddc.app.identity.data.models.document.scan.IDScanResponse
import com.ddc.app.identity.data.models.face.FaceScanResponse
import com.ddc.app.identity.domain.repository.ScanRepositoryInterface
import com.ddc.app.identity.services.ScanParameterService
import com.facetec.sdk.FaceTecIDScanResult
import com.facetec.sdk.FaceTecSessionResult
import kotlinx.coroutines.flow.StateFlow
import org.json.JSONObject
import retrofit2.Response

class ScanRepository(
    private val apiService: ApiServiceInterface,
    private val authRepository: AuthRepositoryInterface,
    private val scanParameterService: ScanParameterService,
) : ScanRepositoryInterface {
    val authState: StateFlow<AuthState> = authRepository.authState

    override suspend fun processDocumentScanResult(
        result: FaceTecIDScanResult,
        context: Context,
        onComplete: (Boolean, IDScanResponse?, String?) -> Unit
    ) {
        val parameters = scanParameterService.buildDocumentScanParameters(result)
        if (parameters != null) {
            try {
                val response: Response<IDScanResponse> = apiService.documentScan(
                    authToken = authRepository.getAuthToken(),
                    body = scanParameterService.buildRequestBody(parameters)
                )
                if (!response.isSuccessful) {
                    val errorBody = response.errorBody()?.string()
                    val apiMessage = errorBody?.let {
                        try { JSONObject(it).optString("message", it) } catch (_: Exception) { it }
                    } ?: "Document scan failed"
                    AppLogger.e("Error while processing IDScan: $apiMessage")
                    onComplete(false, null, apiMessage)
                    return
                }
                val scanResponse: IDScanResponse? = response.body()
                onComplete(true, scanResponse, null)
            } catch (e: Exception) {
                val apiMessage = e.message ?: "Document scan failed"
                AppLogger.e("Error while processing response: $apiMessage")
                onComplete(false, null, apiMessage)
            }
        } else {
            onComplete(false, null, null)
        }
    }

    override suspend fun processFaceScanResult(
        result: FaceTecSessionResult,
        context: Context,
        onComplete: (Boolean, FaceScanResponse?) -> Unit
    ) {
        val parameters = scanParameterService.buildFaceScanParameters(result)
        if (parameters != null) {
            val response: Response<FaceScanResponse> = apiService.faceScan(
                authToken = authRepository.getAuthToken(),
                body = scanParameterService.buildRequestBody(parameters)
            )
            val scanResponse: FaceScanResponse? = response.body()
            if (!response.isSuccessful) {
                AppLogger.e("Error while processing FaceScan: $response")
                onComplete(false, scanResponse)
                return
            }

            onComplete(true, scanResponse)
        } else {
            onComplete(false, null)
        }
    }
}
