package com.ddc.app.core.utils

import android.content.Context
import android.content.pm.PackageManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class NfcSupportChecker(
    private val context: Context
) {
    private val _isNfcSupported = MutableStateFlow(true)
    val isNfcSupported: StateFlow<Boolean> = _isNfcSupported.asStateFlow()

    init {
        checkNfcSupport()
    }

    private fun checkNfcSupport() {
        val hasNfc = context.packageManager.hasSystemFeature(PackageManager.FEATURE_NFC)
        _isNfcSupported.value = hasNfc
    }

    fun recheckNfcSupport() {
        checkNfcSupport()
    }
}