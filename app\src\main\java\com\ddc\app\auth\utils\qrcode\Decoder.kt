package com.ddc.app.auth.utils.qrcode

import com.ddc.app.core.utils.AppLogger
import com.google.zxing.BarcodeFormat
import com.google.zxing.BinaryBitmap
import com.google.zxing.DecodeHintType
import com.google.zxing.MultiFormatReader
import com.google.zxing.PlanarYUVLuminanceSource
import com.google.zxing.common.HybridBinarizer

class Decoder {
    fun decodeQrCode(bytes: ByteArray, width: Int, height: Int): String? {
        val source = PlanarYUVLuminanceSource(
            bytes,
            width,
            height,
            0,
            0,
            width,
            height,
            false
        )
        val binaryBmp = BinaryBitmap(HybridBinarizer(source))

        return try {
            val result = MultiFormatReader().apply {
                setHints(
                    mapOf(
                        DecodeHintType.POSSIBLE_FORMATS to arrayListOf(
                            BarcodeFormat.QR_CODE
                        )
                    )
                )
            }.decode(binaryBmp)
            result.text
        } catch (e: Exception) {
            null
        }
    }
}
