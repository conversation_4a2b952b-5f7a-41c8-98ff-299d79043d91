package com.ddc.app.core.utils

import android.content.Context
import android.os.Build
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File

class RootChecker(
    private val context: Context
) {
    private val _isDeviceRooted = MutableStateFlow(false)
    val isDeviceRooted: StateFlow<Boolean> = _isDeviceRooted.asStateFlow()

    init {
        checkRootStatus()
    }

    private fun checkRootStatus() {
        _isDeviceRooted.value = isRooted()
    }

    private fun isRooted(): Boolean {
        return checkRootBinary() || checkTestKeys() || checkRootManagementApps()
    }

    private fun checkRootBinary(): Boolean {
        val paths = arrayOf(
            "/system/bin/su",
            "/system/xbin/su",
            "/sbin/su",
            "/su/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/xbin/which/su",
            "/data/adb/su"
        )
        return paths.any { path -> File(path).exists() }
    }

    private fun checkTestKeys(): Boolean {
        val buildTags = Build.TAGS
        return buildTags != null && buildTags.contains("test-keys")
    }

    private fun checkRootManagementApps(): Boolean {
        val rootApps = arrayOf(
            "com.topjohnwu.magisk",
            "eu.chainfire.supersu",
            "com.koushikdutta.superuser",
            "com.noshufou.android.su"
        )
        return rootApps.any { appPackage ->
            try {
                context.packageManager.getPackageInfo(appPackage, 0)
                true
            } catch (e: Exception) {
                false
            }
        }
    }
}