package com.ddc.app.identity.presentation.screens

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ddc.app.R
import com.ddc.app.shared.presentation.components.ActionButton
import com.ddc.app.shared.presentation.theme.ColorSuccess

@Composable
fun IdentityStatusScreen(
    paintResource: Int = R.drawable.image_success,
    text: String = "",
    buttonText: String? = "",
    onClick: () -> Unit = {}
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceEvenly,
        modifier = Modifier.padding(30.dp)
    ) {
        Image(
            painter = painterResource(id = paintResource),
            contentDescription = null,
            contentScale = ContentScale.FillHeight,
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp)
        )
        Spacer(modifier = Modifier.height(50.dp))
        Text(
            modifier = Modifier.padding(20.dp),
            text = text,
            fontSize = 20.sp,
            textAlign = TextAlign.Center,
            lineHeight = 30.sp
        )
        Spacer(modifier = Modifier.height(50.dp))
        if (buttonText != null) {
            ActionButton(
                buttonText = buttonText,
                onClick = onClick,
                backgroundColor = ColorSuccess
            )
        }
    }
}

