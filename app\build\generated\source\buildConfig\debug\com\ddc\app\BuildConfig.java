/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.ddc.app;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String APPLICATION_ID = "com.ddc.app";
  public static final String BUILD_TYPE = "debug";
  public static final int VERSION_CODE = 32;
  public static final String VERSION_NAME = "2";
  // Field from default config.
  public static final String NEW_RELIC_TOKEN = "AA296182d1bfe44bb9e7fcabe29f3203642906497f-NRMA";
}
