package com.ddc.app.identity.data.models.document.scan

data class ExtractedInfo(
    val title: String,
    val firstName: String,
    val fullName: String,
    val otherNames1: String,
    val otherNames2: String,
    val personalNumber: String,
    val permanentAddress: String,
    val custodyInformation: String,
    val dateOfIssue: String,
    val fullDateOfBirth: String,
    val placeOfBirth: String,
    val issuingAuthority: String,
    val mrzData: String,
    val personalSummary: String,
    val profession: String,
    val signingCertificate: String,
    val telephone: String,
    val wasSignedDataValidated: Boolean
)