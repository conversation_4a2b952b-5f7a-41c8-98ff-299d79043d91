package com.ddc.app.core.network

import com.ddc.app.auth.data.models.AuthenticateResponse
import com.ddc.app.auth.data.models.TokenResponse
import com.ddc.app.auth.data.models.UserRequest
import com.ddc.app.core.constants.ApiEndPoints
import com.ddc.app.core.data.AppVersionResponseWrapper
import com.ddc.app.identity.data.models.ApplicantSessionData
import com.ddc.app.identity.data.models.document.scan.IDScanResponse
import com.ddc.app.identity.data.models.face.FaceScanResponse
import com.ddc.app.identity.data.response.facetec.SessionResponse
import com.ddc.app.identity.data.response.facetec.SessionTokenResponse
import com.ddc.app.shared.constants.QueryKeys
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST

interface ApiServiceInterface {
    @FormUrlEncoded
    @POST(ApiEndPoints.TOKEN)
    suspend fun getToken(
        @Field(QueryKeys.APPLICANT_ID) applicantId: String,
        @Field(QueryKeys.DOCUMENT_ID) documentId: String
    ): Response<TokenResponse>

    @POST(ApiEndPoints.LOGIN)
    suspend fun login(
        @Header("Authorization") authToken: String,
        @Body body: UserRequest
    ): Response<AuthenticateResponse>

    @POST(ApiEndPoints.LOGOUT)
    suspend fun logout(
        @Header("Authorization") authToken: String
    ): Response<AuthenticateResponse>

    @GET(ApiEndPoints.SESSION_DETAILS)
    suspend fun getUserSessionDetails(
        @Header("Authorization") authToken: String
    ): Response<ApplicantSessionData>

    @POST(ApiEndPoints.CONFIG_KEYS)
    suspend fun getConfigKeys(
        @Header("Authorization") authToken: String,
        @Body body: UserRequest
    ): Response<SessionResponse>

    @POST(ApiEndPoints.SESSION_TOKEN)
    suspend fun getSessionToken(
        @Header("Authorization") authToken: String,
        @Body body: UserRequest
    ): Response<SessionTokenResponse>

    @POST(ApiEndPoints.DOC_SCAN)
    suspend fun documentScan(
        @Header("Authorization") authToken: String,
        @Body body: RequestBody
    ): Response<IDScanResponse>

    @POST(ApiEndPoints.FACE_SCAN)
    suspend fun faceScan(
        @Header("Authorization") authToken: String,
        @Body body: RequestBody
    ): Response<FaceScanResponse>

    @GET(ApiEndPoints.CHECK_APP_VERSION)
    suspend fun checkAppVersion(): Response<AppVersionResponseWrapper>
}