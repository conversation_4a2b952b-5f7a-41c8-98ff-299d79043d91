package com.ddc.app.identity.domain.repository

import android.content.Context
import com.ddc.app.identity.data.models.document.scan.IDScanResponse
import com.ddc.app.identity.data.models.face.FaceScanResponse
import com.facetec.sdk.FaceTecIDScanResult
import com.facetec.sdk.FaceTecSessionResult

interface ScanRepositoryInterface {
    suspend fun processFaceScanResult(
        result: FaceTecSessionResult,
        context: Context,
        onComplete: (Boolean, FaceScanResponse?) -> Unit
    )

    suspend fun processDocumentScanResult(
        result: FaceTecIDScanResult,
        context: Context,
        onComplete: (Boolean, IDScanResponse?, String?) -> Unit
    )
}