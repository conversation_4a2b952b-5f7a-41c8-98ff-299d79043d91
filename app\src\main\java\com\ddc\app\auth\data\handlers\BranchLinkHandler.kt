package com.ddc.app.auth.data.handlers

import android.net.Uri
import com.ddc.app.shared.constants.QueryKeys
import io.branch.indexing.BranchUniversalObject
import io.branch.referral.util.LinkProperties
import io.branch.referral.Branch
import org.json.JSONObject

interface BranchLinkHandlerInterface {
    fun extractDeepLinkParams(
        branchUniversalObject: BranchUniversalObject?,
        linkProperties: LinkProperties?
    ): Pair<String?, String?>

    fun extractParamsFromUrl(url: String): Pair<String?, String?>
}

class BranchLinkHandler : BranchLinkHandlerInterface {
    override fun extractDeepLinkParams(
        branchUniversalObject: BranchUniversalObject?,
        linkProperties: LinkProperties?
    ): Pair<String?, String?> {
        val controlParams = linkProperties?.controlParams ?: return Pair(null, null)
        return extractFromControlParams(controlParams)
    }

    private fun extractFromControlParams(controlParams: Any): Pair<String?, String?> {
        return when (controlParams) {
            is Map<*, *> -> extractFromMap(controlParams)
            is JSONObject -> extractFromJson(controlParams)
            else -> Pair(null, null)
        }
    }

    private fun extractFromMap(controlParams: Map<*, *>): Pair<String?, String?> {
        val applicantId = controlParams[QueryKeys.APPLICANT_ID] as? String
        val documentId = controlParams[QueryKeys.DOCUMENT_ID] as? String

        if (applicantId != null && documentId != null) {
            return Pair(applicantId, documentId)
        }

        return extractFromUrlFields(controlParams)
    }

    private fun extractFromJson(controlParams: JSONObject): Pair<String?, String?> {
        val applicantId = controlParams.optString(QueryKeys.APPLICANT_ID).takeIf { it.isNotEmpty() }
        val documentId = controlParams.optString(QueryKeys.DOCUMENT_ID).takeIf { it.isNotEmpty() }
        return Pair(applicantId, documentId)
    }

    private fun extractFromUrlFields(controlParams: Map<*, *>): Pair<String?, String?> {
        val urlFields = listOf("\$canonical_url", "\$deeplink_path")

        for (field in urlFields) {
            val urlValue = controlParams[field] as? String
            if (!urlValue.isNullOrEmpty()) {
                val extractedParams = extractParamsFromUrl(urlValue)
                if (extractedParams.first != null && extractedParams.second != null) {
                    return extractedParams
                }
            }
        }

        return Pair(null, null)
    }

    override fun extractParamsFromUrl(url: String): Pair<String?, String?> {
        val uri = Uri.parse(url)
        val applicantId = uri.getQueryParameter(QueryKeys.APPLICANT_ID)
        val documentId = uri.getQueryParameter(QueryKeys.DOCUMENT_ID)
        return Pair(applicantId, documentId)
    }
}
