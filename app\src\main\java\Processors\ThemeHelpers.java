package Processors;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.Typeface;
import com.ddc.app.identity.utils.FaceTecGuidance;

import com.ddc.app.R;

import com.facetec.sdk.FaceTecCancelButtonCustomization;
import com.facetec.sdk.FaceTecCustomization;
import com.facetec.sdk.FaceTecSDK;

public class ThemeHelpers {

    public static void setAppTheme(Context context, String theme) {
        Config.currentCustomization = getCustomizationForTheme(context, theme);
        Config.currentLowLightCustomization = getLowLightCustomizationForTheme(context, theme);
        Config.currentDynamicDimmingCustomization = getDynamicDimmingCustomizationForTheme(context, theme);

        FaceTecGuidance.setVocalGuidanceSoundFiles();

        FaceTecSDK.setCustomization(Config.currentCustomization);
        FaceTecSDK.setLowLightCustomization(Config.currentLowLightCustomization);
        FaceTecSDK.setDynamicDimmingCustomization(Config.currentDynamicDimmingCustomization);
    }

    public static FaceTecCustomization getCustomizationForTheme(Context context, String theme) {
        FaceTecCustomization currentCustomization = new FaceTecCustomization();

        int[] retryScreenSlideshowImages = new int[]{R.mipmap.ideal_image_1, R.mipmap.ideal_image_2, R.mipmap.ideal_image_3, R.mipmap.ideal_image_4, R.mipmap.ideal_image_5};

        if (theme.equals("Pseudo-Fullscreen")) {
            int primaryColor = Color.parseColor("#92011b");
            int primaryColorLight = Color.parseColor("#BA0B18");
            int secondaryColor = Color.parseColor("#92011b");
            int backgroundColor = Color.parseColor("#EEF6F8");
            int buttonBackgroundDisabledColor = Color.parseColor("#adadad");
            int ddcAppLogoImage = R.drawable.app_logo;
            Typeface boldTypeface = Typeface.create("sans-serif", Typeface.BOLD);
            Typeface normalTypeface = Typeface.create("sans-serif", Typeface.NORMAL);
            currentCustomization.getOverlayCustomization().brandingImage = ddcAppLogoImage;
            currentCustomization.getOverlayCustomization().backgroundColor = backgroundColor;
            currentCustomization.getOverlayCustomization().showBrandingImage = false;
            currentCustomization.getGuidanceCustomization().backgroundColors = backgroundColor;
            currentCustomization.getGuidanceCustomization().foregroundColor = primaryColor;
            currentCustomization.getGuidanceCustomization().headerFont = boldTypeface;
            currentCustomization.getGuidanceCustomization().subtextFont = normalTypeface;
            currentCustomization.getGuidanceCustomization().buttonFont = boldTypeface;
            currentCustomization.getGuidanceCustomization().buttonTextNormalColor = backgroundColor;
            currentCustomization.getGuidanceCustomization().buttonBackgroundNormalColor = primaryColor;
            currentCustomization.getGuidanceCustomization().buttonTextHighlightColor = backgroundColor;
            currentCustomization.getGuidanceCustomization().buttonBackgroundHighlightColor = Color.parseColor("#565656");
            currentCustomization.getGuidanceCustomization().buttonTextDisabledColor = backgroundColor;
            currentCustomization.getGuidanceCustomization().buttonBackgroundDisabledColor = buttonBackgroundDisabledColor;
            currentCustomization.getGuidanceCustomization().buttonBorderColor = Color.TRANSPARENT;
            currentCustomization.getGuidanceCustomization().buttonBorderWidth = 0;
            currentCustomization.getGuidanceCustomization().buttonCornerRadius = 30;
            currentCustomization.getGuidanceCustomization().readyScreenOvalFillColor = Color.TRANSPARENT;
            currentCustomization.getGuidanceCustomization().readyScreenTextBackgroundColor = backgroundColor;
            currentCustomization.getGuidanceCustomization().readyScreenTextBackgroundCornerRadius = 5;
            currentCustomization.getGuidanceCustomization().retryScreenImageBorderColor = primaryColor;
            currentCustomization.getGuidanceCustomization().retryScreenImageBorderWidth = 2;
            currentCustomization.getGuidanceCustomization().retryScreenImageCornerRadius = 10;
            currentCustomization.getGuidanceCustomization().retryScreenOvalStrokeColor = backgroundColor;
            currentCustomization.getGuidanceCustomization().retryScreenSlideshowImages = retryScreenSlideshowImages;
            currentCustomization.getGuidanceCustomization().retryScreenSlideshowInterval = 2000;
            currentCustomization.getGuidanceCustomization().enableRetryScreenSlideshowShuffle = true;
            currentCustomization.getGuidanceCustomization().cameraPermissionsScreenImage = R.mipmap.camera_shutter_offblack;
            currentCustomization.getResultScreenCustomization().backgroundColors = backgroundColor;
            currentCustomization.getResultScreenCustomization().foregroundColor = primaryColor;
            currentCustomization.getResultScreenCustomization().messageFont = boldTypeface;
            currentCustomization.getResultScreenCustomization().activityIndicatorColor = primaryColor;
            currentCustomization.getResultScreenCustomization().customActivityIndicatorImage = 0;
            currentCustomization.getResultScreenCustomization().customActivityIndicatorRotationInterval = 800;
            currentCustomization.getResultScreenCustomization().customActivityIndicatorAnimation = R.drawable.pseudo_fullscreen_animated_activity_indicator;
            currentCustomization.getResultScreenCustomization().resultAnimationBackgroundColor = secondaryColor;
            currentCustomization.getResultScreenCustomization().resultAnimationForegroundColor = backgroundColor;
            currentCustomization.getResultScreenCustomization().resultAnimationSuccessBackgroundImage = 0;
            currentCustomization.getResultScreenCustomization().resultAnimationUnsuccessBackgroundImage = 0;
            currentCustomization.getResultScreenCustomization().customResultAnimationSuccess = R.drawable.image_success;
            currentCustomization.getResultScreenCustomization().customResultAnimationUnsuccess = R.drawable.pseudo_fullscreen_animated_unsuccess;
            currentCustomization.getResultScreenCustomization().showUploadProgressBar = true;
            currentCustomization.getResultScreenCustomization().uploadProgressTrackColor = Color.parseColor("#332B2B2B");
            currentCustomization.getResultScreenCustomization().uploadProgressFillColor = secondaryColor;
            currentCustomization.getResultScreenCustomization().animationRelativeScale = 1.0f;
            currentCustomization.getResultScreenCustomization().resultAnimationDisplayTime = 2.0;
            currentCustomization.getFeedbackCustomization().backgroundColors = secondaryColor;
            currentCustomization.getFeedbackCustomization().textColor = backgroundColor;
            currentCustomization.getFeedbackCustomization().textFont = boldTypeface;
            currentCustomization.getFeedbackCustomization().cornerRadius = 5;
            currentCustomization.getFeedbackCustomization().elevation = 10;
            currentCustomization.getFrameCustomization().backgroundColor = backgroundColor;
            currentCustomization.getFrameCustomization().borderColor = primaryColor;
            currentCustomization.getFrameCustomization().borderWidth = 0;
            currentCustomization.getFrameCustomization().cornerRadius = 0;
            currentCustomization.getFrameCustomization().elevation = 0;
            currentCustomization.getOvalCustomization().strokeColor = primaryColor;
            currentCustomization.getOvalCustomization().progressColor1 = Color.parseColor("#BF3BC371");
            currentCustomization.getOvalCustomization().progressColor2 = Color.parseColor("#BF3BC371");
            currentCustomization.getCancelButtonCustomization().customImage = R.mipmap.single_chevron_left_offblack;
            currentCustomization.getCancelButtonCustomization().setLocation(FaceTecCancelButtonCustomization.ButtonLocation.CUSTOM);
            currentCustomization.getCancelButtonCustomization().setCustomLocation(new Rect(10, 10, 25, 25));
            currentCustomization.getCancelButtonCustomization().hideForCameraPermissions = false;
            currentCustomization.getInitialLoadingAnimationCustomization().backgroundColors = backgroundColor;
            currentCustomization.getInitialLoadingAnimationCustomization().foregroundColor = primaryColor;
            currentCustomization.getInitialLoadingAnimationCustomization().customAnimation = R.drawable.pseudo_fullscreen_animated_activity_indicator;
            currentCustomization.getInitialLoadingAnimationCustomization().customAnimationImage = 0;
            currentCustomization.getInitialLoadingAnimationCustomization().customAnimationImageRotationInterval = 800;
            currentCustomization.getInitialLoadingAnimationCustomization().animationRelativeScale = 1.0f;
            currentCustomization.getInitialLoadingAnimationCustomization().defaultAnimationBackgroundColor = Color.parseColor("#E6E6E6");
            currentCustomization.getInitialLoadingAnimationCustomization().defaultAnimationForegroundColor = primaryColor;
            currentCustomization.getInitialLoadingAnimationCustomization().messageFont = boldTypeface;
            currentCustomization.getGuidanceCustomization().readyScreenHeaderFont = boldTypeface;
            currentCustomization.getGuidanceCustomization().readyScreenHeaderTextColor = primaryColor;
            currentCustomization.getGuidanceCustomization().readyScreenSubtextFont = normalTypeface;
            currentCustomization.getGuidanceCustomization().readyScreenSubtextTextColor = Color.parseColor("#565656");
            currentCustomization.getGuidanceCustomization().retryScreenHeaderFont = boldTypeface;
            currentCustomization.getGuidanceCustomization().retryScreenHeaderTextColor = primaryColor;
            currentCustomization.getGuidanceCustomization().retryScreenSubtextFont = normalTypeface;
            currentCustomization.getGuidanceCustomization().retryScreenSubtextTextColor = Color.parseColor("#565656");
            currentCustomization.getIdScanCustomization().standaloneIDScanWatermark = R.drawable.app_logo;
            currentCustomization.getIdScanCustomization().faceMatchToIDBrandingImage = 0;
            currentCustomization.getIdScanCustomization().buttonBackgroundNormalColor = primaryColor;
            currentCustomization.getIdScanCustomization().buttonBackgroundHighlightColor = primaryColorLight;
            currentCustomization.getIdScanCustomization().captureScreenFocusMessageTextColor =  primaryColor;
            currentCustomization.getIdScanCustomization().captureScreenTextBackgroundColor =  primaryColor;
            currentCustomization.getIdScanCustomization().activeTorchButtonImage = R.drawable.torch_active_black;
            currentCustomization.getIdScanCustomization().inactiveTorchButtonImage = R.drawable.torch_inactive_black;
            currentCustomization.getIdScanCustomization().customNFCStartingAnimation = R.drawable.nfc_scan_animation;
            currentCustomization.getIdScanCustomization().customNFCScanningAnimation = R.drawable.hold_nfc_animation;
            currentCustomization.getIdScanCustomization().selectionScreenDocumentImage = R.drawable.passdetail;
            currentCustomization.getIdScanCustomization().selectionScreenForegroundColor = primaryColor;
            currentCustomization.getIdScanCustomization().buttonBorderColor = primaryColor;
            currentCustomization.getIdScanCustomization().captureFrameStrokeColor = primaryColor;
            currentCustomization.getIdScanCustomization().reviewScreenTextBackgroundColor = primaryColor;
        }

        return currentCustomization;
    }

    static FaceTecCustomization getLowLightCustomizationForTheme(Context context, String theme) {
        FaceTecCustomization currentLowLightCustomization = getCustomizationForTheme(context, theme);

        int[] retryScreenSlideshowImages = new int[]{R.mipmap.ideal_image_1, R.mipmap.ideal_image_2, R.mipmap.ideal_image_3, R.mipmap.ideal_image_4, R.mipmap.ideal_image_5};

        return currentLowLightCustomization;
    }

    static FaceTecCustomization getDynamicDimmingCustomizationForTheme(Context context, String theme) {
        FaceTecCustomization currentDynamicDimmingCustomization = getCustomizationForTheme(context, theme);

        int[] retryScreenSlideshowImages = new int[]{R.mipmap.ideal_image_1, R.mipmap.ideal_image_2, R.mipmap.ideal_image_3, R.mipmap.ideal_image_4, R.mipmap.ideal_image_5};

        if(theme.equals("Pseudo-Fullscreen")) {
            int primaryColor = Color.parseColor("#EEF6F8");
            int primaryColorLight = Color.WHITE;
            int secondaryColor = Color.parseColor("#3BC371");
            int backgroundColor = Color.BLACK;

            currentDynamicDimmingCustomization.getOverlayCustomization().brandingImage = 0;
            currentDynamicDimmingCustomization.getGuidanceCustomization().foregroundColor = primaryColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().buttonTextNormalColor = backgroundColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().buttonBackgroundNormalColor = primaryColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().buttonTextHighlightColor = backgroundColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().buttonBackgroundHighlightColor = Color.WHITE;
            currentDynamicDimmingCustomization.getGuidanceCustomization().buttonTextDisabledColor = backgroundColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().buttonBackgroundDisabledColor = Color.parseColor("#BFEEF6F8");
            currentDynamicDimmingCustomization.getGuidanceCustomization().buttonBorderColor = Color.TRANSPARENT;
            currentDynamicDimmingCustomization.getGuidanceCustomization().readyScreenOvalFillColor = Color.TRANSPARENT;
            currentDynamicDimmingCustomization.getGuidanceCustomization().readyScreenTextBackgroundColor = backgroundColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().retryScreenImageBorderColor = primaryColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().retryScreenOvalStrokeColor = backgroundColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().retryScreenSlideshowImages = retryScreenSlideshowImages;
            currentDynamicDimmingCustomization.getResultScreenCustomization().foregroundColor = primaryColor;
            currentDynamicDimmingCustomization.getResultScreenCustomization().activityIndicatorColor = primaryColor;
            currentDynamicDimmingCustomization.getResultScreenCustomization().customActivityIndicatorImage = 0;
            currentDynamicDimmingCustomization.getResultScreenCustomization().customActivityIndicatorAnimation = R.drawable.pseudo_fullscreen_animated_activity_indicator_offwhite;
            currentDynamicDimmingCustomization.getResultScreenCustomization().resultAnimationBackgroundColor = secondaryColor;
            currentDynamicDimmingCustomization.getResultScreenCustomization().resultAnimationForegroundColor = backgroundColor;
            currentDynamicDimmingCustomization.getResultScreenCustomization().resultAnimationSuccessBackgroundImage = 0;
            currentDynamicDimmingCustomization.getResultScreenCustomization().resultAnimationUnsuccessBackgroundImage = 0;
            currentDynamicDimmingCustomization.getResultScreenCustomization().customResultAnimationSuccess = R.drawable.pseudo_fullscreen_animated_success_offwhite;
            currentDynamicDimmingCustomization.getResultScreenCustomization().customResultAnimationUnsuccess = R.drawable.pseudo_fullscreen_animated_unsuccess_offwhite;
            currentDynamicDimmingCustomization.getResultScreenCustomization().uploadProgressTrackColor = Color.parseColor("#332B2B2B");
            currentDynamicDimmingCustomization.getResultScreenCustomization().uploadProgressFillColor = secondaryColor;
            currentDynamicDimmingCustomization.getFeedbackCustomization().backgroundColors = secondaryColor;
            currentDynamicDimmingCustomization.getFeedbackCustomization().textColor = backgroundColor;
            currentDynamicDimmingCustomization.getFrameCustomization().borderColor = primaryColor;
            currentDynamicDimmingCustomization.getOvalCustomization().strokeColor = primaryColor;
            currentDynamicDimmingCustomization.getOvalCustomization().progressColor1 = Color.parseColor("#BF3BC371");
            currentDynamicDimmingCustomization.getOvalCustomization().progressColor2 = Color.parseColor("#BF3BC371");
            currentDynamicDimmingCustomization.getCancelButtonCustomization().customImage = R.mipmap.single_chevron_left_offwhite;
            currentDynamicDimmingCustomization.getInitialLoadingAnimationCustomization().foregroundColor = primaryColor;
            currentDynamicDimmingCustomization.getInitialLoadingAnimationCustomization().customAnimation = R.drawable.pseudo_fullscreen_animated_activity_indicator_offwhite;
            currentDynamicDimmingCustomization.getInitialLoadingAnimationCustomization().customAnimationImage = 0;
            currentDynamicDimmingCustomization.getInitialLoadingAnimationCustomization().defaultAnimationBackgroundColor = primaryColor;
            currentDynamicDimmingCustomization.getInitialLoadingAnimationCustomization().defaultAnimationForegroundColor = secondaryColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().readyScreenHeaderTextColor = primaryColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().readyScreenSubtextTextColor = primaryColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().retryScreenHeaderTextColor = primaryColor;
            currentDynamicDimmingCustomization.getGuidanceCustomization().retryScreenSubtextTextColor = primaryColor;
        }

        return currentDynamicDimmingCustomization;
    }
}
