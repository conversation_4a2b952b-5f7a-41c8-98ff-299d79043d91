package com.ddc.app.auth.presentation.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.ddc.app.R
import com.ddc.app.auth.presentation.components.StartButton
import com.ddc.app.auth.presentation.components.carousel.ImageCarousel
import com.ddc.app.core.presentation.navigation.Routes
import com.ddc.app.shared.constants.AppTexts
import com.ddc.app.shared.presentation.components.AppLogo

@Composable
fun HomeScreen(
    navController: NavController,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.SpaceBetween,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(25.dp))
        AppLogo()
        ImageCarousel(
            images = authImages()
        )
        StartButton(
            onClick = { navController.navigate(Routes.qrCodeScannerScreen) }
        )
        Spacer(modifier = Modifier.height(25.dp))
    }
}

private fun authImages() = listOf(
    R.mipmap.walkthrough_one to AppTexts.SCAN_QR_CODE,
    R.mipmap.walkthrough_two to AppTexts.SCAN_DOCUMENT,
    R.mipmap.walkthrough_three to AppTexts.FACE_SCAN
)