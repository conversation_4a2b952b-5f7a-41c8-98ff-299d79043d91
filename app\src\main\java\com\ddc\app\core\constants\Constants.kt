package com.ddc.app.core.constants

object Constants {
    const val VERSION = "2.0.0"
    const val TIMEOUT = 60L
    const val LOGGER_TAG_NAME = "AppLogger"

    const val SSL_HASH = "sha256/en8y3bA/ygQ6hI8XYt00Kgmi2pBQSVCs+DvaZRV3lqU="
    const val SSL_PIN_DOMAIN = "api.ddc.uk.net"

    const val ERROR_INVALID_QR_CODE = "Unable to process QR code. Please ensure it is a valid QR Code."
    const val ERROR_AUTH_TOKEN_FAILED = "Failed to obtain authentication token."
    const val ERROR_LOGIN_FAILED_AFTER_LOGOUT = "Login failed after logout attempt."
}