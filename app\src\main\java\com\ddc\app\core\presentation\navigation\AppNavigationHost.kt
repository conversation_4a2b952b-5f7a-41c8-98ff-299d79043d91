package com.ddc.app.core.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import com.ddc.app.auth.presentation.navigation.authenticationGraph
import com.ddc.app.shared.presentation.components.sidemenu.SidebarMenu

@Composable
fun AppNavigationHost(
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    SidebarMenu { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = Routes.authenticate,
            modifier = modifier
        ) {
            authenticationGraph(navController)
        }
    }
}