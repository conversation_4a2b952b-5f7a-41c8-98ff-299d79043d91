package com.ddc.app.auth.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ddc.app.auth.data.repositories.AuthRepository
import com.ddc.app.auth.data.repositories.AuthState

import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.auth.data.handlers.BranchLinkHandlerInterface
import com.ddc.app.core.constants.Constants
import com.ddc.app.core.utils.AppError
import com.ddc.app.core.utils.ErrorHandler
import com.ddc.app.core.utils.AppLogger
import com.ddc.app.shared.constants.QueryKeys
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class QRCodeScannerViewModel(
    private val authRepository: AuthRepositoryInterface,
    private val branchLinkHandler: BranchLinkHandlerInterface,
) : ViewModel() {

    val authState: StateFlow<AuthState> = authRepository.authState.stateIn(
        viewModelScope,
        SharingStarted.WhileSubscribed(),
        AuthState()
    )

    private val _errorState = MutableStateFlow<AppError?>(null)
    val errorState: StateFlow<AppError?> = _errorState

    fun handleScannedLink(link: String, onComplete: (Boolean) -> Unit) {
        if (isBranchLink(link)) {
            resolveBranchLinkViaSdk(link, onComplete)
        } else {
            val (applicantId, documentId) = branchLinkHandler.extractParamsFromUrl(link)
            if (applicantId != null && documentId != null) {
                updateAuthStateAndComplete(applicantId, documentId, onComplete)
            } else {
                handleInvalidQrCode(onComplete)
            }
        }
    }

    private fun isBranchLink(link: String): Boolean =
        link.contains("app.link") || link.contains("branch.io")

    private fun updateAuthStateAndComplete(
        applicantId: String,
        documentId: String,
        onComplete: (Boolean) -> Unit
    ) {
        launchInScope {
            authRepository.updateAuthState(
                authState.value.copy(
                    applicantId = applicantId,
                    documentId = documentId
                )
            )
            onComplete(true)
        }
    }

    private fun handleInvalidQrCode(onComplete: (Boolean) -> Unit) {
        _errorState.value = ErrorHandler.customError(Constants.ERROR_INVALID_QR_CODE)
        onComplete(false)
    }

    private fun resolveBranchLinkViaSdk(link: String, onComplete: (Boolean) -> Unit) {
        launchInScope {
            try {
                val latestParams = io.branch.referral.Branch.getInstance().latestReferringParams
                if (latestParams != null && latestParams.length() > 0) {
                    val (applicantId, documentId) = extractParamsFromBranchJson(latestParams)
                    if (applicantId != null && documentId != null) {
                        updateAuthStateAndComplete(applicantId, documentId, onComplete)
                        return@launchInScope
                    }
                }

                val redirectUrl = getBranchRedirectUrl(link)
                if (redirectUrl != null) {
                    val (applicantId, documentId) = branchLinkHandler.extractParamsFromUrl(redirectUrl)
                    if (applicantId != null && documentId != null) {
                        updateAuthStateAndComplete(applicantId, documentId, onComplete)
                    } else {
                        _errorState.value = ErrorHandler.customError(Constants.ERROR_INVALID_QR_CODE)
                    }
                } else {
                    _errorState.value = ErrorHandler.customError(Constants.ERROR_INVALID_QR_CODE)
                }
            } catch (e: Exception) {
                _errorState.value = ErrorHandler.customError(Constants.ERROR_INVALID_QR_CODE)
            }
        }
    }

    private fun extractParamsFromBranchJson(params: org.json.JSONObject): Pair<String?, String?> {
        var applicantId = params.optString(QueryKeys.APPLICANT_ID).takeIf { it.isNotEmpty() }
        var documentId = params.optString(QueryKeys.DOCUMENT_ID).takeIf { it.isNotEmpty() }

        if (applicantId != null && documentId != null) {
            return Pair(applicantId, documentId)
        }

        val urlFields = listOf("\$canonical_url", "\$deeplink_path", "\$android_fallback_url")
        for (field in urlFields) {
            val urlValue = params.optString(field)
            if (urlValue.isNotEmpty()) {
                val extractedParams = branchLinkHandler.extractParamsFromUrl(urlValue)
                if (extractedParams.first != null && extractedParams.second != null) {
                    return extractedParams
                }
            }
        }

        return Pair(applicantId, documentId)
    }

    fun doAuthenticate(onAuthentication: (Result<Boolean>) -> Unit) {
        launchInScope {
            authRepository.requestAuthToken().fold(
                onSuccess = { token ->
                    if (token != null) {
                        authRepository.setAuthStateAuthToken(token)
                        var logoutAttempted = false
                        suspend fun attemptLogin() = authRepository.login()
                        suspend fun attemptLogout() = authRepository.logout()

                        val loginResult = attemptLogin()
                        if (loginResult.isSuccess && loginResult.getOrNull() == true) {
                            onAuthentication(Result.success(true))
                        } else if (!logoutAttempted) {
                            logoutAttempted = true
                            attemptLogout()
                            attemptLogin().fold(
                                onSuccess = { loginSuccess ->
                                    onAuthentication(Result.success(loginSuccess))
                                },
                                onFailure = { loginError ->
                                    val appError = if (loginError is AuthRepository.ApiException) loginError.appError else ErrorHandler.handleError(loginError)
                                    _errorState.value = appError
                                    onAuthentication(Result.success(false))
                                }
                            )
                        } else {
                            _errorState.value = ErrorHandler.customError(Constants.ERROR_LOGIN_FAILED_AFTER_LOGOUT)
                            onAuthentication(Result.success(false))
                        }
                    } else {
                        _errorState.value = ErrorHandler.customError(Constants.ERROR_AUTH_TOKEN_FAILED)
                        onAuthentication(Result.success(false))
                    }
                },
                onFailure = { error ->
                    val appError = if (error is AuthRepository.ApiException) error.appError else ErrorHandler.handleError(error)
                    _errorState.value = appError
                    onAuthentication(Result.failure(error))
                }
            )
        }
    }

    fun handleBranchParams(applicantId: String, documentId: String, onSuccess: () -> Unit) {
        launchInScope {
            authRepository.updateAuthState(
                authState.value.copy(
                    applicantId = applicantId,
                    documentId = documentId
                )
            )
            doAuthenticate { authResult ->
                authResult.onSuccess { authenticated ->
                    if (authenticated) {
                        onSuccess()
                    }
                }
            }
        }
    }

    fun handleBranchDeepLink(
        branchUniversalObject: io.branch.indexing.BranchUniversalObject?,
        linkProperties: io.branch.referral.util.LinkProperties?,
        onSuccess: () -> Unit
    ) {
        val (applicantId, documentId) = branchLinkHandler.extractDeepLinkParams(branchUniversalObject, linkProperties)
        if (applicantId != null && documentId != null) {
            handleBranchParams(applicantId, documentId, onSuccess)
        }
    }

    fun onErrorShown() {
        _errorState.value = null
        launchInScope {
            authRepository.updateAuthState(AuthState())
        }
    }

    private suspend fun getBranchRedirectUrl(branchUrl: String): String? {
        return kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
            try {
                val connection = createHttpConnection(branchUrl)
                val responseCode = connection.responseCode

                if (responseCode in 300..399) {
                    val location = connection.getHeaderField("Location")
                    connection.disconnect()

                    return@withContext when {
                        location == null -> null
                        location.startsWith("ddcapp://") -> location
                        hasRequiredParameters(location) -> location
                        else -> getBranchRedirectUrl(location)
                    }
                } else {
                    connection.disconnect()
                    return@withContext null
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    private fun createHttpConnection(url: String): java.net.HttpURLConnection {
        val connection = java.net.URL(url).openConnection() as java.net.HttpURLConnection
        connection.requestMethod = "GET"
        connection.instanceFollowRedirects = false
        connection.setRequestProperty("User-Agent", "DDC-App-Android")
        connection.connectTimeout = 10000
        connection.readTimeout = 10000
        return connection
    }

    private fun hasRequiredParameters(url: String): Boolean =
        url.contains("applicantid=") && url.contains("documentid=")

    private fun launchInScope(block: suspend () -> Unit) {
        viewModelScope.launch { block() }
    }
}