package com.ddc.app.identity.data.models.document.scan

data class IDScanDto(
    val success: <PERSON><PERSON>an,
    val wasProcessed: <PERSON><PERSON>an,
    val scanResultBlob: String,
    val error: <PERSON><PERSON>an,
    val idScanSessionId: String,
    val isCompletelyDone: <PERSON>olean,
    val isIDScanIncomplete: <PERSON>olean,
    val isReadyForUserConfirmation: <PERSON>olean,
    val nfcStatusEnumInt: Int,
    val nfcAuthenticationStatusEnumInt: Int,
    val digitalIDSpoofStatusEnumInt: Int,
    val photoIDNextStepEnumInt: Int,
    val documentData: String,
    val idScanSecurityChecks: SecurityCheck,
    val mrzValues: MrzValues,
    val nfcValues: NfcValues,
    val scannedValues: ScannedValues,
)