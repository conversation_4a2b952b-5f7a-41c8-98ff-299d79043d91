package com.ddc.app.core.di

import com.ddc.app.core.network.ApiServiceInterface
import com.ddc.app.auth.data.repositories.AuthRepository
import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.core.network.NetworkModule
import com.ddc.app.core.utils.AppUpdateManager
import com.ddc.app.core.utils.ErrorHandler
import org.koin.dsl.module
import com.ddc.app.core.utils.NfcSupportChecker
import org.koin.android.ext.koin.androidContext
import com.ddc.app.core.utils.RootChecker

val coreModule = module {
    single { NetworkModule.retrofit.create(ApiServiceInterface::class.java) }
    single<AuthRepositoryInterface> { AuthRepository(get()) }
    single {
        NfcSupportChecker(context = androidContext())
    }
    single {
        <PERSON><PERSON>he<PERSON>(
            context = androidContext()
        )
    }
    single { AppUpdateManager() }
    single { ErrorHandler }
}