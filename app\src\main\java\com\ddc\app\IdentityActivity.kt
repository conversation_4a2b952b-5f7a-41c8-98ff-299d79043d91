package com.ddc.app

import Processors.Config
import Processors.Processor
import Processors.ThemeHelpers
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.ddc.app.core.utils.AppError
import com.ddc.app.identity.data.models.DeviceConfigData
import com.ddc.app.identity.data.models.ScanType
import com.ddc.app.identity.domain.repository.IdentityCaptureRepositoryInterface
import com.ddc.app.identity.domain.repository.ScanRepositoryInterface
import com.ddc.app.identity.processors.DocumentScanProcessor
import com.ddc.app.identity.processors.FaceScanProcessor
import com.ddc.app.identity.presentation.screens.IdentityCaptureScreen
import com.ddc.app.identity.presentation.screens.IdentityStatusScreen
import com.ddc.app.identity.utils.FaceTecGuidance
import com.ddc.app.shared.constants.AppTexts
import com.ddc.app.shared.presentation.components.ErrorMessageDisplay
import com.ddc.app.shared.presentation.components.TryAgainAction
import com.ddc.app.shared.presentation.components.sidemenu.SidebarMenu
import com.ddc.app.shared.presentation.components.LoadingScreen
import com.ddc.app.shared.presentation.theme.DDCIDTheme
import com.facetec.sdk.FaceTecIDScanResult
import com.facetec.sdk.FaceTecSDK
import com.ddc.app.MainActivity
import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.auth.data.repositories.AuthState
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import com.ddc.app.R

class IdentityActivity : ComponentActivity() {
    private lateinit var deviceKeyIdentifier: String
    private lateinit var encryptionKey: String
    private lateinit var productionKeyText: String
    private var utils: FaceTecGuidance = FaceTecGuidance()
    private val scanRepository: ScanRepositoryInterface by inject()
    private val identityCaptureRepository: IdentityCaptureRepositoryInterface by inject()
    private val authRepository: AuthRepositoryInterface by inject()
    private var latestProcessor: Processor? = null
    private var latestIDScanResult: FaceTecIDScanResult? = null
    private var isSessionPreparingToLaunch = false
    private val _isDeviceConfigLoaded = MutableLiveData<Boolean>(false)
    private val isDeviceConfigLoaded: LiveData<Boolean> get() = _isDeviceConfigLoaded
    private val _isReady = MutableLiveData<Boolean>(false)
    private val isReady: LiveData<Boolean> get() = _isReady
    private val _isCompleted = MutableLiveData<Boolean>(false)
    private val isCompleted: LiveData<Boolean> get() = _isCompleted
    private val _faceScanResult = MutableLiveData<AppError?>(null)
    private val faceScanResult: LiveData<AppError?> get() = _faceScanResult

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        isDeviceConfigLoaded.observe(this, Observer { isLoaded ->
            if (isLoaded) {
                initialiseFaceTecSDK()
            }
        })

        lifecycleScope.launch {
            FaceTecSDK.preload(this@IdentityActivity).let {
                fetchDeviceConfig()
            }
        }

        setContent {
            DDCIDTheme {
                val navController = rememberNavController()
                val isReadyState by isReady.observeAsState(false)
                val isCompletedState by isCompleted.observeAsState(false)
                val faceScanResultState by faceScanResult.observeAsState(null)

                Scaffold(
                    modifier = Modifier.fillMaxWidth(),
                    content = { paddingValues ->
                        Box(modifier = Modifier.fillMaxSize()) {
                            if (!isReadyState) {
                                LoadingScreen()
                            } else {
                                SidebarMenu {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                        verticalArrangement = Arrangement.Center,
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .padding(paddingValues)
                                    ) {
                                        NavHost(
                                            navController = navController,
                                            startDestination = if (isCompletedState) "identityStatus" else "identityCapture"
                                        ) {
                                            composable("identityStatus") {
                                                if (isCompletedState) {
                                                    IdentityStatusScreen(
                                                        text = AppTexts.SCAN_SUCCESS,
                                                        buttonText = AppTexts.LABEL_FINISH.uppercase(),
                                                        onClick = { finishVerification() }
                                                    )
                                                }
                                            }
                                            composable("identityCapture") {
                                                IdentityCaptureScreen(
                                                    startScanSession = { startScanSession() },
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                            faceScanResultState?.let { error ->
                                ErrorMessageDisplay(
                                    error = error,
                                    onDismiss = { onErrorShown() },
                                    navController = navController,
                                    tryAgainAction = TryAgainAction.DoNothing,
                                )
                            }
                        }
                    }
                )
            }
        }
    }

    private suspend fun fetchDeviceConfig() {
        fetchIdentitySessionDetails()?.let { data: DeviceConfigData ->
            if (data.deviceKeyIdentifier.isBlank() ||
                data.encryptionKey.isBlank() ||
                data.productionKeyText.isBlank()
            ) {
                setDeviceConfigLoaded(false)
                return
            }

            deviceKeyIdentifier = data.deviceKeyIdentifier
            encryptionKey = data.encryptionKey
            productionKeyText = data.productionKeyText
            setDeviceConfigLoaded(true)
        }
    }

    private suspend fun fetchIdentitySessionDetails(): DeviceConfigData? {
        return identityCaptureRepository.fetchIdentitySessionDetails()
    }

    private fun startScan(processorFactory: (String) -> Processor) {
        lifecycleScope.launch {
            identityCaptureRepository.getSessionToken()
                .onSuccess { sessionToken ->
                    latestProcessor = processorFactory(sessionToken)
                }
                .onFailure {
                    setStartButtonState(true)
                }
        }
    }

    private fun startScanSession() {
        latestIDScanResult = null
        setStartButtonState(false)
        observeJourneyCompleteness()
        isSessionPreparingToLaunch = true

        val currentState = identityCaptureRepository.identityState.value
        if (currentState.isCompleted()) {
            setStartButtonState(true)
            return
        }

        if (!currentState.documentScan) {
            startScan { sessionToken ->
                isSessionPreparingToLaunch = false
                DocumentScanProcessor(
                    sessionToken = sessionToken,
                    context = this,
                    scanRepository = scanRepository,
                )
            }
        } else if (!currentState.faceScan) {
            startScan { sessionToken ->
                isSessionPreparingToLaunch = false
                FaceScanProcessor(
                    sessionToken = sessionToken,
                    context = this,
                    scanRepository = scanRepository,
                )
            }
        } else {
            setStartButtonState(true)
        }
    }

    private fun initialiseFaceTecSDK() {
        Config.initializeFaceTecSDK(
            this,
            productionKeyText,
            deviceKeyIdentifier,
            encryptionKey
        ) { successful: Boolean ->
            initialiseCallback(successful) {
                setStartButtonState(successful)
            }
        }
    }

    private fun initialiseCallback(successful: Boolean, onCompletion: (Boolean) -> Unit) {
        if (successful) {
            ThemeHelpers.setAppTheme(this, utils.currentTheme)
            FaceTecGuidance.setVocalGuidanceSoundFiles()
        }
        return onCompletion(successful)
    }

    fun setLatestIDScanResult(idScanResult: FaceTecIDScanResult?) {
        latestIDScanResult = idScanResult
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        latestProcessor ?: return

        latestProcessor?.let { processor ->
            if (processor.isSuccess()) {
                identityCaptureRepository.clearIdentityStateData()
                when (processor) {
                    is DocumentScanProcessor -> {
                        latestProcessor = null
                        identityCaptureRepository.updateScanCompleted(ScanType.DOCUMENT)
                    }
                    is FaceScanProcessor -> {
                        latestProcessor = null
                        identityCaptureRepository.updateScanCompleted(ScanType.FACE)
                    }
                }
                startScanSession()
            } else {
                latestProcessor = null
                if (processor is FaceScanProcessor) {
                    showScanError("Face scan failed. Please try again.")
                }
                setStartButtonState(true)
            }
        }
    }

    override fun onBackPressed() {
        if (isSessionPreparingToLaunch) {
            return
        }
        super.onBackPressed()
    }

    private fun setStartButtonState(isEnabled: Boolean) {
        _isReady.postValue(isEnabled)
    }

    private fun setDeviceConfigLoaded(isLoaded: Boolean) {
        _isDeviceConfigLoaded.postValue(isLoaded)
    }

    private fun observeJourneyCompleteness() {
        lifecycleScope.launch {
            identityCaptureRepository.identityState.collect { identityState ->
                _isCompleted.value = identityState.isCompleted()
            }
        }
    }

    private fun onErrorShown() {
        _faceScanResult.value = null
        setStartButtonState(true)
    }

    fun showScanError(message: String) {
        _faceScanResult.postValue(AppError.GenericError(message))
        setStartButtonState(true)
    }

    private fun finishVerification() {
        lifecycleScope.launch {
            authRepository.logout()
            authRepository.updateAuthState(AuthState())
            identityCaptureRepository.clearIdentityStateData()
            val intent = Intent(this@IdentityActivity, MainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
            finish()
        }
    }
}