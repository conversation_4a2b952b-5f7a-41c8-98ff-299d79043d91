package com.ddc.app.identity.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ddc.app.R
import com.ddc.app.auth.data.repositories.AuthState
import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.identity.domain.repository.IdentityCaptureRepositoryInterface
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class IdentityCaptureViewModel(
    authRepository: AuthRepositoryInterface,
    private val identityCaptureRepository: IdentityCaptureRepositoryInterface,
) : ViewModel() {

    val authState: StateFlow<AuthState> = authRepository.authState

    init {
        viewModelScope.launch {
            identityCaptureRepository.setApplicantDetails()
        }
    }

    fun overviewImages() = listOf(
        R.drawable.image_scan_document to "Scan document",
        R.drawable.image_chip_read to "Chip read",
        R.drawable.image_face_scan to "Face scan"
    )
}