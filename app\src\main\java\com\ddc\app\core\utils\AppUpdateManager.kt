package com.ddc.app.core.utils

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.ddc.app.core.network.ApiServiceInterface
import com.ddc.app.core.data.AppVersionResponseWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

data class AppUpdateState(
    val showUpdateMessage: MutableState<Boolean> = mutableStateOf(false),
    val serverVersion: MutableState<Int?> = mutableStateOf(null),
    val updateError: MutableState<AppError?> = mutableStateOf(null),
    val isSoftUpdate: MutableState<Boolean> = mutableStateOf(false)
)

class AppUpdateManager : KoinComponent {
    private val apiService: ApiServiceInterface by inject()
    private val errorHandler: <PERSON><PERSON>r<PERSON>and<PERSON> by inject()

    fun checkForUpdates(
        currentVersionCode: Int,
        coroutineScope: CoroutineScope,
        updateState: AppUpdateState = AppUpdateState()
    ): AppUpdateState {
        coroutineScope.launch {
            try {
                val response: AppVersionResponseWrapper? = apiService.checkAppVersion().body()
                if (response != null) {
                    val appVersionResponse = response.data
                    val serverVersionCode = appVersionResponse.android_version?.toIntOrNull()
                    if (serverVersionCode != null && isUpdateRequired(currentVersionCode, serverVersionCode)) {
                        updateState.showUpdateMessage.value = true
                        updateState.serverVersion.value = serverVersionCode
                        updateState.updateError.value = errorHandler.customError(
                            "Please update this app because we have made some improvements in the newer version. \n\nYou should only use the latest version which includes recent security updates."
                        )
                        updateState.isSoftUpdate.value = appVersionResponse.update_type == "soft_update"
                    }
                }
            } catch (e: Exception) {
                updateState.updateError.value = errorHandler.handleError(e)
            }
        }
        return updateState
    }

    private fun isUpdateRequired(currentVersionCode: Int, serverVersionCode: Int): Boolean {
        return serverVersionCode > currentVersionCode
    }
}