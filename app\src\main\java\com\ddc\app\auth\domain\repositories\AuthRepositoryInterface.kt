package com.ddc.app.auth.domain.repositories

import com.ddc.app.auth.data.models.UserRequest
import com.ddc.app.auth.data.repositories.AuthState
import kotlinx.coroutines.flow.StateFlow
import org.json.JSONObject

interface AuthRepositoryInterface {
    val authState: StateFlow<AuthState>
    suspend fun updateAuthState(newState: AuthState)
    suspend fun login(): Result<Boolean>
    suspend fun authenticateUser(): Result<Boolean>
    suspend fun logout(): Result<Boolean>
    suspend fun requestAuthToken(): Result<String?>
    suspend fun setAuthStateAuthToken(token: String)
    fun userRequest(): UserRequest
    fun getUserData(): MutableMap<String, String?>
    fun userJsonData(): JSONObject
    fun getAuthToken(): String
}