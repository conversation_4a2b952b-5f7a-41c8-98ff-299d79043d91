package com.ddc.app.shared.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ddc.app.shared.presentation.theme.ColorSuccess

@Composable
fun ActionButton(
    modifier: Modifier = Modifier,
    buttonText: String,
    onClick: () -> Unit,
    backgroundColor: Color = ColorSuccess
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        contentAlignment = Alignment.BottomCenter
    ) {
        TextButton(
            modifier = modifier
                .clip(RoundedCornerShape(8.dp))
                .background(backgroundColor)
                .fillMaxWidth(),
            onClick = { onClick() }) {
            Text(
                text = buttonText,
                fontSize = 20.sp,
                color = Color.White
            )
        }
    }
}