package com.ddc.app.auth.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ddc.app.shared.constants.AppTexts
import com.ddc.app.shared.presentation.theme.ColorPrimary

@Composable
fun StartButton(
    onClick: () -> Unit,
    text: String = AppTexts.START_NOW
) {
    Box(
        contentAlignment = Alignment.BottomCenter,
        modifier = Modifier.fillMaxSize()
    ) {
        TextButton(
            onClick = { onClick() },
            modifier = Modifier
                .background(ColorPrimary)
                .fillMaxWidth()
        ) {
            Text(
                text = text,
                color = Color.White,
                fontSize = 18.sp
            )
        }
    }
}
