package com.ddc.app.auth.presentation.navigation

import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.navigation
import androidx.navigation.compose.composable
import com.ddc.app.auth.presentation.screens.HomeScreen
import com.ddc.app.auth.presentation.screens.QRCodeScannerScreen
import com.ddc.app.core.presentation.navigation.Routes
import com.ddc.app.identity.presentation.screens.IdentityCaptureScreen

fun NavGraphBuilder.authenticationGraph(navController: NavController) {
    navigation(
        startDestination = Routes.homeScreen,
        route = Routes.authenticate
    ) {
        composable(Routes.homeScreen) {
            HomeScreen(navController = navController)
        }

        composable(Routes.qrCodeScannerScreen) {
            QRCodeScannerScreen(navController = navController)
        }
    }
}