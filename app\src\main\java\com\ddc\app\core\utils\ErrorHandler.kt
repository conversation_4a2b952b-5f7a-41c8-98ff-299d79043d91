package com.ddc.app.core.utils

sealed class AppError {
    data class NetworkError(val message: String) : AppError()
    data class ApiError(val code: Int, val message: String) : AppError()
    data class GenericError(val message: String) : AppError()
}

object ErrorHandler {
    fun handleError(throwable: Throwable): AppError {
        return when (throwable) {
            is retrofit2.HttpException -> {
                val code = throwable.code()
                val message = throwable.message()
                AppError.ApiError(code, getApiErrorMessage(code, message))
            }
            is java.io.IOException -> AppError.NetworkError("No internet connection. Please check your network.")
            else -> AppError.GenericError("An unexpected error occurred: ${throwable.message}")
        }
    }

    private fun getApiErrorMessage(code: Int, defaultMessage: String): String {
        return when (code) {
            400 -> "Bad request. Please check your input."
            401 -> "Unauthorized. Please log in again."
            403 -> "Access denied. You don’t have permission."
            404 -> "Resource not found."
            500 -> "Server error. Please try again later."
            else -> defaultMessage
        }
    }

    fun customError(message: String): AppError {
        return AppError.GenericError(message)
    }
}