package com.ddc.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.compose.rememberNavController
import com.ddc.app.core.utils.AppUpdateManager
import com.ddc.app.core.utils.AppUpdateState
import com.ddc.app.core.utils.NetworkMonitor
import com.ddc.app.core.utils.NfcSupportChecker
import com.ddc.app.core.utils.RootChecker
import com.ddc.app.core.presentation.navigation.AppNavigationHost
import com.ddc.app.shared.presentation.components.ErrorMessageDisplay
import com.ddc.app.shared.presentation.components.NoInternetScreen
import com.ddc.app.shared.presentation.components.NoNfcSupportScreen
import com.ddc.app.shared.presentation.components.RootedDeviceScreen
import com.ddc.app.shared.presentation.components.TryAgainAction
import com.ddc.app.shared.presentation.theme.DDCIDTheme
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import com.ddc.app.auth.presentation.viewmodels.QRCodeScannerViewModel
import android.content.Intent
import android.net.Uri
import com.newrelic.agent.android.NewRelic
import com.ddc.app.shared.constants.QueryKeys
import io.branch.referral.Branch

class MainActivity : ComponentActivity() {

    private val networkMonitor: NetworkMonitor by inject()
    private val nfcSupportChecker: NfcSupportChecker by inject()
    private val rootChecker: RootChecker by inject()
    private val appUpdateManager: AppUpdateManager by inject()
    private val qrCodeScannerViewModel: QRCodeScannerViewModel by viewModel()

    private fun handleBranchDeepLink() {
        Branch.sessionBuilder(this).withCallback { branchUniversalObject, linkProperties, error ->
            if (error == null) {
                qrCodeScannerViewModel.handleBranchDeepLink(branchUniversalObject, linkProperties) {
                    startActivity(Intent(this, IdentityActivity::class.java))
                    finish()
                }
            }
        }.withData(intent?.data).init()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        NewRelic.withApplicationToken(BuildConfig.NEW_RELIC_TOKEN).start(applicationContext)
        enableEdgeToEdge()

        setContent {
            DDCIDTheme {
                val navController = rememberNavController()
                val isConnected by networkMonitor.isConnected.collectAsState()
                val isNfcSupported by nfcSupportChecker.isNfcSupported.collectAsState()
                val isDeviceRooted by rootChecker.isDeviceRooted.collectAsState()
                val coroutineScope = rememberCoroutineScope()
                val updateState = remember { AppUpdateState() }

                LaunchedEffect(Unit) {
                    appUpdateManager.checkForUpdates(BuildConfig.VERSION_CODE, coroutineScope, updateState)
                }

                Scaffold(
                    modifier = Modifier.fillMaxWidth(),
                    content = { paddingValues ->
                        AppNavigationHost(
                            navController = navController,
                            modifier = Modifier.padding(paddingValues)
                        )
                    }
                )

                if (!isConnected) {
                    NoInternetScreen(
                        onRetry = {
                            networkMonitor.checkInternetConnection()
                        },
                        onDismissRequest = { },
                        modifier = Modifier
                    )
                }

                if (!isNfcSupported) {
                    NoNfcSupportScreen(
                        onRetry = {
                            nfcSupportChecker.recheckNfcSupport()
                        },
                        onDismissRequest = { },
                        modifier = Modifier
                    )
                }

                if (isDeviceRooted) {
                    RootedDeviceScreen(
                        onDismissRequest = { },
                        modifier = Modifier
                    )
                }

                if (updateState.showUpdateMessage.value) {
                    ErrorMessageDisplay(
                        titleText = "App update",
                        error = updateState.updateError.value,
                        onDismiss = { updateState.showUpdateMessage.value = false },
                        navController = navController,
                        tryAgainAction = TryAgainAction.OpenExternalUrl("market://details?id=${BuildConfig.APPLICATION_ID}"),
                        primaryButtonText = "Update Now",
                        secondaryButtonText = "Update Later",
                        showSecondaryButton = updateState.isSoftUpdate.value,
                        onSecondaryButtonClick = { updateState.showUpdateMessage.value = false },
                    )
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()
        handleBranchDeepLink()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        this.intent = intent

        if (intent.hasExtra("branch_force_new_session") &&
            intent.getBooleanExtra("branch_force_new_session", false)) {

            Branch.sessionBuilder(this).withCallback { branchUniversalObject, linkProperties, error ->
                if (error == null) {
                    qrCodeScannerViewModel.handleBranchDeepLink(branchUniversalObject, linkProperties) {
                        startActivity(Intent(this, IdentityActivity::class.java))
                        finish()
                    }
                }
            }.reInit()
        } else {
            handleBranchDeepLink()
        }
    }
}