package com.ddc.app.identity.processors

import Processors.Processor
import android.content.Context
import com.ddc.app.IdentityActivity
import com.ddc.app.core.network.NetworkModule
import com.ddc.app.core.utils.AppLogger
import com.ddc.app.identity.domain.repository.ScanRepositoryInterface
import com.facetec.sdk.FaceTecFaceScanProcessor
import com.facetec.sdk.FaceTecFaceScanResultCallback
import com.facetec.sdk.FaceTecSessionActivity
import com.facetec.sdk.FaceTecSessionResult
import com.facetec.sdk.FaceTecSessionStatus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class FaceScanProcessor(
    sessionToken: String,
    context: Context,
    private val scanRepository: ScanRepositoryInterface
) : Processor(), FaceTecFaceScanProcessor {
    private var success = false
    private var identityActivity: IdentityActivity = context as IdentityActivity

    init {
        FaceTecSessionActivity.createAndLaunchSession(identityActivity, this, sessionToken);
    }

    override fun processSessionWhileFaceTecSDKWaits(
        sessionResult: FaceTecSessionResult,
        faceScanResultCallback: FaceTecFaceScanResultCallback
    ) {
        if (sessionResult.status != FaceTecSessionStatus.SESSION_COMPLETED_SUCCESSFULLY) {
            AppLogger.d("Session was not completed successfully, cancelling.")
            NetworkModule.cancelPendingRequests()
            faceScanResultCallback.cancel();
            return;
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                scanRepository.processFaceScanResult(
                    sessionResult,
                    identityActivity
                ) { completed, responseJSON ->
                    success = true
                    faceScanResultCallback.cancel()
                }
            } catch (e: Exception) {
                AppLogger.d("Error processing IDScan: ${e.message}")
                faceScanResultCallback.cancel()
                return@launch
            }
        }
    }

    override fun isSuccess(): Boolean {
        return success
    }
}