package com.ddc.app.shared.presentation.theme

import androidx.compose.ui.graphics.Color

val ColorPrimary = Color(0xFF92011B)
val ColorSuccess = Color(0xFF4AAB4A)
val ColorPrimaryLight = Color(0xFFC51A29)
val ColorPrimaryDark = Color(0xFF6A0014)
val ColorSecondary = Color(0xFF1B9290)
val ColorSecondaryLight = Color(0xFF4BB7B4)
val ColorSecondaryDark = Color(0xFF007C7A)
val ColorAccent = Color(0xFFFFC107)
val ColorBackground = Color(0xFFF5F5F5)
val ColorText = Color(0xFF212121)
val ColorError = Color(0xFFD32F2F)
val ColorWhite = Color.White
val ColorGray = Color.Gray