package com.ddc.app.identity.presentation.components.identity

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ddc.app.auth.data.repositories.AuthState
import com.ddc.app.shared.constants.AppTexts

@Composable
fun WelcomeMessage(authState: AuthState) {
    Column {
        Text(
            text = AppTexts.WELCOME,
            color = Color.Black,
            fontSize = 28.sp,
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(22.dp))
        Text(
            text = "${authState.userFirstname} ${authState.userLastname}",
            color = Color.Black,
            fontSize = 24.sp
        )
        Spacer(modifier = Modifier.height(22.dp))
        Text(
            text = AppTexts.IDENTITY_WELCOME_INSTRUCTIONS_ONE,
            color = Color.Black,
            fontSize = 18.sp
        )
        Spacer(modifier = Modifier.height(22.dp))
        Text(
            text = AppTexts.IDENTITY_WELCOME_INSTRUCTION_SECOND,
            color = Color.Black,
            fontSize = 18.sp
        )
        Spacer(modifier = Modifier.height(22.dp))
    }
}
