<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportHeight="24.0"
    android:viewportWidth="24.0">
    <group
        android:name="strokeGroup"
        android:pivotX="12"
        android:pivotY="12"
        android:rotation="90.0">

        <path
            android:name="strokePath"
            android:pathData="M 12, 12
                        m 0, -10
                        a 10,-10 0 1,0 0,20
                        a 10,-10 0 1,0 0,-20"
            android:strokeColor="#EEF6F8"
            android:strokeWidth="0"
            android:trimPathStart="1"
            android:strokeLineCap="round"
            android:strokeLineJoin="miter"/>
    </group>

    <group android:name="check">
        <path
            android:name="tick"
            android:pathData="M7.35,12.74 l0,0 l0,0"
            android:strokeColor="#3BC371"
            android:strokeWidth="1"
            android:strokeMiterLimit="10" />
    </group>
</vector>