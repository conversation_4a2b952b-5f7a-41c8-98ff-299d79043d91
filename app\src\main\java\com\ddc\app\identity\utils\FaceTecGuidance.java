package com.ddc.app.identity.utils;

import com.facetec.sdk.FaceTecVocalGuidanceCustomization;

import Processors.Config;

public class FaceTecGuidance
{
    public FaceTecGuidance() {

    }

    enum VocalGuidanceMode {
        OFF,
        MINIMAL,
        FULL
    }

    private android.media.MediaPlayer vocalGuidanceOnPlayer;
    private android.media.MediaPlayer vocalGuidanceOffPlayer;
    static com.ddc.app.identity.utils.FaceTecGuidance.VocalGuidanceMode vocalGuidanceMode = com.ddc.app.identity.utils.FaceTecGuidance.VocalGuidanceMode.MINIMAL;

    public String currentTheme = "Pseudo-Fullscreen";
    private android.os.Handler themeTransitionTextHandler;

    public static void setVocalGuidanceSoundFiles() {
        Config.currentCustomization.vocalGuidanceCustomization.pleaseFrameYourFaceInTheOvalSoundFile = com.ddc.app.R.raw.please_frame_your_face_sound_file;
        Config.currentCustomization.vocalGuidanceCustomization.pleaseMoveCloserSoundFile = com.ddc.app.R.raw.please_move_closer_sound_file;
        Config.currentCustomization.vocalGuidanceCustomization.pleaseRetrySoundFile = com.ddc.app.R.raw.please_retry_sound_file;
        Config.currentCustomization.vocalGuidanceCustomization.uploadingSoundFile = com.ddc.app.R.raw.uploading_sound_file;
        Config.currentCustomization.vocalGuidanceCustomization.facescanSuccessfulSoundFile = com.ddc.app.R.raw.facescan_successful_sound_file;
        Config.currentCustomization.vocalGuidanceCustomization.pleasePressTheButtonToStartSoundFile = com.ddc.app.R.raw.please_press_button_sound_file;

        switch (vocalGuidanceMode) {
            case OFF:
                Config.currentCustomization.vocalGuidanceCustomization.mode = FaceTecVocalGuidanceCustomization.VocalGuidanceMode.NO_VOCAL_GUIDANCE;
                break;
            case MINIMAL:
                Config.currentCustomization.vocalGuidanceCustomization.mode = FaceTecVocalGuidanceCustomization.VocalGuidanceMode.MINIMAL_VOCAL_GUIDANCE;
                break;
            case FULL:
                Config.currentCustomization.vocalGuidanceCustomization.mode = FaceTecVocalGuidanceCustomization.VocalGuidanceMode.FULL_VOCAL_GUIDANCE;
                break;
        }
    }
}
