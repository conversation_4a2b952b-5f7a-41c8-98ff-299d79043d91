[versions]
agp = "8.7.3"
gson = "2.10.1"
koinAndroid = "4.0.0"
koinAndroidxCompose = "4.0.0"
kotlin = "2.0.0"
coreKtx = "1.15.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
kotlinxCoroutinesAndroid = "1.7.3"
kotlinxCoroutinesCore = "1.7.2"
lifecycleRuntimeKtx = "2.8.7"
activityCompose = "1.9.3"
composeBom = "2024.12.01"
appcompat = "1.7.0"
converterGson = "2.9.0"
core = "3.5.3"
json = "20210307"
koinAndroidCompat = "4.0.0"
constraintlayout = "2.2.0"
drawerlayout = "1.2.0"
lifecycleViewmodelCompose = "2.8.7"
loggingInterceptor = "4.9.3"
loggingInterceptorVersion = "4.11.0"
material = "1.6.0"
navigationCompose = "2.8.4"
navVersion = "2.8.4"
retrofit = "2.11.0"
runtime = "1.7.5"
runtimeLivedata = "1.7.5"
runtimeRxjava2 = "1.7.5"
securityCrypto = "1.1.0-alpha06"
zxingAndroidEmbedded = "4.3.0"
securityCryptoKtx = "1.0.0"
cameraCore = "1.4.0"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-navigation-compose-v284 = { module = "androidx.navigation:navigation-compose", version.ref = "navVersion" }
androidx-runtime-livedata = { module = "androidx.compose.runtime:runtime-livedata", version.ref = "runtimeLivedata" }
androidx-runtime-rxjava2 = { module = "androidx.compose.runtime:runtime-rxjava2", version.ref = "runtimeRxjava2" }
camera-lifecycle = { module = "androidx.camera:camera-lifecycle" }
com-google-zxing-core = { module = "com.google.zxing:core" }
google-gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
insert-koin-koin-android = { module = "io.insert-koin:koin-android", version.ref = "koinAndroid" }
insert-koin-koin-androidx-compose = { module = "io.insert-koin:koin-androidx-compose", version.ref = "koinAndroidxCompose" }
insert-koin-koin-core = { module = "io.insert-koin:koin-core" }
io-insert-koin-koin-core = { module = "io.insert-koin:koin-core" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
activity-compose = { module = "androidx.activity:activity-compose" }
androidx-activity-activity-compose = { module = "androidx.activity:activity-compose" }
androidx-activity-activity-compose2 = { module = "androidx.activity:activity-compose" }
androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "appcompat" }
androidx-appcompat-resources = { module = "androidx.appcompat:appcompat-resources", version.ref = "appcompat" }
androidx-camera-camera2 = { module = "androidx.camera:camera-camera2" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle" }
androidx-camera-view = { module = "androidx.camera:camera-view" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycleViewmodelCompose" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx" }
androidx-lifecycle-viewmodel-savedstate = { module = "androidx.lifecycle:lifecycle-viewmodel-savedstate" }
androidx-material = { module = "androidx.compose.material:material" }
androidx-navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navigationCompose" }
androidx-runtime = { module = "androidx.compose.runtime:runtime", version.ref = "runtime" }
androidx-security-crypto = { module = "androidx.security:security-crypto", version.ref = "securityCrypto" }
appcompat = { module = "androidx.appcompat:appcompat" }
camera-camera2 = { module = "androidx.camera:camera-camera2" }
camera-view = { module = "androidx.camera:camera-view" }
core = { module = "com.google.zxing:core" }
facetec-facetec-sdk = { module = "com.facetec:facetec-sdk" }
facetec-sdk = { module = "com.facetec.android:facetec-sdk" }
google-core = { module = "com.google.zxing:core" }
gson = { module = "com.google.code.gson:gson" }
journeyapps-zxing-android-embedded = { module = "com.journeyapps:zxing-android-embedded", version.ref = "zxingAndroidEmbedded" }
json = { module = "org.json:json", version.ref = "json" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-drawerlayout = { group = "androidx.drawerlayout", name = "drawerlayout", version.ref = "drawerlayout" }
koin-android = { module = "io.insert-koin:koin-android" }
koin-android-compat = { module = "io.insert-koin:koin-android-compat", version.ref = "koinAndroidCompat" }
koin-androidx-compose = { module = "io.insert-koin:koin-androidx-compose" }
koin-androidx-compose-navigation = { module = "io.insert-koin:koin-androidx-compose-navigation" }
koin-core = { module = "io.insert-koin:koin-core" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinxCoroutinesAndroid" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutinesCore" }
logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "loggingInterceptor" }
logging-interceptor-v4110 = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "loggingInterceptorVersion" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
okhttp = { module = "com.squareup.okhttp3:okhttp" }
okhttp3-logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor" }
okio = { module = "com.squareup.okio:okio" }

retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit2-converter-gson = { module = "com.squareup.retrofit2:converter-gson" }
converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "converterGson" }
retrofit2-retrofit = { module = "com.squareup.retrofit2:retrofit" }
ui = { module = "androidx.compose.ui:ui" }
zxing-android-embedded = { module = "com.journeyapps:zxing-android-embedded" }
zxing-core = { module = "com.google.zxing:core", version.ref = "core" }
androidx-security-crypto-ktx = { group = "androidx.security", name = "security-crypto-ktx", version.ref = "securityCryptoKtx" }
androidx-camera-core = { group = "androidx.camera", name = "camera-core", version.ref = "cameraCore" }


[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

