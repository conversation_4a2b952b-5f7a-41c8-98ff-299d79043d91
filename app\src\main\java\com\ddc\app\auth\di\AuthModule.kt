package com.ddc.app.auth.di

import com.ddc.app.auth.data.handlers.BranchLinkHandler
import com.ddc.app.auth.data.handlers.BranchLinkHandlerInterface
import com.ddc.app.auth.data.repositories.AuthRepository
import com.ddc.app.auth.domain.repositories.AuthRepositoryInterface
import com.ddc.app.auth.presentation.viewmodels.QRCodeScannerViewModel
import org.koin.core.module.Module
import org.koin.core.module.dsl.*
import org.koin.dsl.module

val authModule: Module = module {
    single<AuthRepositoryInterface> { AuthRepository(get()) }
    single<BranchLinkHandlerInterface> { BranchLinkHandler() }
    viewModel {
        QRCodeScannerViewModel(
            authRepository = get(),
            branchLinkHandler = get(),
        )
    }
}