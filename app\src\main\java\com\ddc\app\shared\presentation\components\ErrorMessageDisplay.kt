package com.ddc.app.shared.presentation.components

import android.content.Intent
import android.net.Uri
import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.ddc.app.BuildConfig
import com.ddc.app.core.utils.AppError
import com.ddc.app.shared.presentation.theme.Typography
import com.ddc.app.shared.presentation.theme.ColorPrimary

sealed class TryAgainAction {
    data class NavigateToScreen(val route: String) : TryAgainAction()
    object ExitApp : TryAgainAction()
    object DoNothing : TryAgainAction()
    data class OpenExternalUrl(val url: String) : TryAgainAction()
}

@Composable
fun ErrorMessageDisplay(
    error: AppError?,
    onDismiss: () -> Unit,
    navController: NavController,
    tryAgainAction: TryAgainAction,
    @DrawableRes imageResId: Int? = null,
    primaryButtonText: String = "Try Again",
    secondaryButtonText: String = "Cancel",
    showSecondaryButton: Boolean = false,
    onSecondaryButtonClick: () -> Unit = {},
    titleText: String = "Error"
) {
    if (error == null) return

    val openDialog = remember { mutableStateOf(true) }
    val context = LocalContext.current
    val playStoreUrl = "market://details?id=${BuildConfig.APPLICATION_ID}"

    AnimatedVisibility(
        visible = openDialog.value,
        enter = scaleIn(),
        exit = scaleOut()
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White.copy(alpha = 0.99f))
                .clickable(onClick = {})
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(0.85f)
                    .align(Alignment.Center)
                    .background(Color.White, shape = RoundedCornerShape(16.dp))
                    .shadow(elevation = 1.dp, shape = RoundedCornerShape(6.dp))
                    .padding(16.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = titleText.uppercase(),
                    style = Typography.titleLarge.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 20.sp
                    ),
                    textAlign = TextAlign.Center,
                    color = Color.Black,
                    modifier = Modifier
                        .padding(horizontal = 0.dp)
                        .padding(top = 16.dp, bottom = 24.dp)
                )

                imageResId?.let {
                    Image(
                        painter = painterResource(id = it),
                        contentDescription = "Illustration",
                        modifier = Modifier
                            .size(120.dp)
                            .padding(bottom = 16.dp)
                    )
                }

                Text(
                    text = when (error) {
                        is AppError.NetworkError -> error.message
                        is AppError.ApiError -> error.message
                        is AppError.GenericError -> error.message
                    },
                    style = Typography.bodyMedium.copy(
                        fontSize = 14.sp
                    ),
                    textAlign = TextAlign.Center,
                    color = Color.Black,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 30.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Button(
                        onClick = {
                            openDialog.value = false
                            onDismiss()
                            when (tryAgainAction) {
                                is TryAgainAction.NavigateToScreen -> navController.navigate(tryAgainAction.route)
                                is TryAgainAction.ExitApp -> (context as? android.app.Activity)?.finishAffinity()
                                is TryAgainAction.DoNothing -> Unit
                                is TryAgainAction.OpenExternalUrl -> {
                                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(tryAgainAction.url))
                                    context.startActivity(intent)
                                }
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(40.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = ColorPrimary,
                            contentColor = Color.White
                        ),
                        shape = RoundedCornerShape(1.dp)
                    ) {
                        Text(
                            text = primaryButtonText.uppercase(),
                            style = Typography.bodyLarge.copy(
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )
                        )
                    }

                    if (showSecondaryButton) {
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(
                            onClick = {
                                openDialog.value = false
                                onSecondaryButtonClick()
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(40.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = ColorPrimary,
                                contentColor = Color.White
                            ),
                            shape = RoundedCornerShape(1.dp)
                        ) {
                            Text(
                                text = secondaryButtonText.uppercase(),
                                style = Typography.bodyLarge.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 16.sp
                                )
                            )
                        }
                    }
                }
            }
        }
    }
}