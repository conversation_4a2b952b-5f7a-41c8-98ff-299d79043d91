package com.ddc.app.shared.presentation.components.sidemenu

import android.app.Activity
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.ddc.app.shared.constants.AppTexts
import com.ddc.app.shared.presentation.components.ActionButton
import com.ddc.app.shared.presentation.theme.ColorPrimary
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SidebarMenu(content: @Composable (PaddingValues) -> Unit) {
    val context = LocalContext.current
    val drawerState = rememberDrawerState(initialValue = DrawerValue.Closed)
    val scope = rememberCoroutineScope()

    val sideMenuManager = remember { SideMenuManager(context) }
    val menuItems = sideMenuManager.getMenuItems()

    ModalNavigationDrawer(
        modifier = Modifier,
        drawerState = drawerState,
        drawerContent = {
            ModalDrawerSheet(
                modifier = Modifier
                    .fillMaxWidth(0.75f)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxHeight(),
                    verticalArrangement = Arrangement.SpaceBetween
                ) {
                    Column {
                        Spacer(modifier = Modifier.height(16.dp))
                        menuItems.forEach { item ->
                            NavigationDrawerItem(
                                icon = {
                                    Icon(
                                        painter = painterResource(id = item.icon),
                                        contentDescription = item.title
                                    )
                                },
                                label = { Text(item.title) },
                                selected = false,
                                onClick = {
                                    scope.launch {
                                        drawerState.close()
                                        item.onClick()
                                    }
                                },
                                modifier = Modifier.padding(NavigationDrawerItemDefaults.ItemPadding)
                            )
                        }
                    }

                    ActionButton(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        buttonText = AppTexts.EXIT,
                        backgroundColor = ColorPrimary,
                        onClick = {
                            scope.launch {
                                drawerState.close()
                            }
                        }
                    )
                }
            }
        },
    ) {
        Scaffold(
            topBar = {
                SidebarTopAppBar(
                    title = "",
                    onOpenDrawer = { scope.launch { drawerState.open() } }
                )
            }
        ) { paddingValues ->
            Box { content(paddingValues) }
        }
    }
}
