package com.ddc.app.shared.presentation.components.sidemenu

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.ddc.app.shared.presentation.theme.ColorPrimary
import com.ddc.app.shared.presentation.theme.ColorWhite

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SidebarTopAppBar(
    title: String,
    onOpenDrawer: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = { Text(title) },
        navigationIcon = {
            IconButton(onClick = onOpenDrawer) {
                Icon(
                    imageVector = Icons.Default.Menu,
                    contentDescription = "Open navigation drawer",
                    tint = ColorWhite
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = ColorPrimary,
            titleContentColor = ColorWhite,
            navigationIconContentColor = ColorWhite
        ),
        modifier = modifier
    )
}