package com.ddc.app.shared.presentation.components.sidemenu

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import androidx.core.net.toUri
import com.ddc.app.R
import com.ddc.app.core.utils.AppLogger
import com.ddc.app.shared.constants.AppTexts
import com.ddc.app.shared.constants.Constants

class SideMenuManager(private val context: Context) {

    fun getMenuItems(): List<DrawerMenuItem> = listOf(
        DrawerMenuItem("FAQ", R.drawable.faq) { openUrl(Constants.FAQ_URL) },
        DrawerMenuItem("Privacy Policy", R.drawable.privacypolicy) { openUrl(Constants.PRIVACY_POLICY_URL) },
        DrawerMenuItem("About Us", R.mipmap.image_about_us) { openUrl(Constants.ABOUT_US_URL) },
        DrawerMenuItem("Contact Us", R.mipmap.image_menu_call) { sendEmail(Constants.CONTACT_US_EMAIL) },
        DrawerMenuItem("Rate App", R.mipmap.image_rate_app) { rateApp() },
        DrawerMenuItem("Share App", R.mipmap.image_share_app) { shareApp() }
    )

    private fun openUrl(url: String) {
        launchIntent(Intent(Intent.ACTION_VIEW, url.toUri()))
    }

    private fun sendEmail(mailto: String) {
        launchIntent(Intent(Intent.ACTION_SENDTO, mailto.toUri()))
    }

    private fun rateApp() {
        val playStoreIntent = Intent(Intent.ACTION_VIEW, "market://details?id=${context.packageName}".toUri())
        val fallbackIntent = Intent(Intent.ACTION_VIEW, "https://play.google.com/store/apps/details?id=${context.packageName}".toUri())
        launchIntent(playStoreIntent, fallbackIntent)
    }

    private fun shareApp() {
        val shareIntent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, AppTexts.APP_SHARE_TEXT)
        }
        launchIntent(shareIntent)
    }

    private fun launchIntent(intent: Intent, fallbackIntent: Intent? = null) {
        try {
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            AppLogger.e("Failed to open activity", e)
            fallbackIntent?.let { context.startActivity(it) }
        }
    }
}
