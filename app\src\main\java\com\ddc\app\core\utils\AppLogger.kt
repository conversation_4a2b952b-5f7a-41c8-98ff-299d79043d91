package com.ddc.app.core.utils

import android.util.Log
import com.ddc.app.BuildConfig
import com.ddc.app.core.constants.Constants

object AppLogger {
    private val IN_DEBUG = BuildConfig.DEBUG
    private const val TAG = Constants.LOGGER_TAG_NAME

    fun d(message: String) {
        if (IN_DEBUG) {
            Log.d(TAG, message)
        }
    }

    fun e(message: String, throwable: Throwable? = null) {
        if (IN_DEBUG) {
            Log.e(TAG, message, throwable)
        }
    }

    fun i(message: String) {
        if (IN_DEBUG) {
            Log.i(TAG, message)
        }
    }

    fun w(message: String) {
        if (IN_DEBUG) {
            Log.w(TAG, message)
        }
    }
}
