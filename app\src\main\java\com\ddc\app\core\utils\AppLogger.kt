package com.ddc.app.core.utils

import android.util.Log
import com.ddc.app.BuildConfig
import com.ddc.app.core.constants.Constants

object AppLogger {
    private val IN_DEBUG = BuildConfig.DEBUG
    private const val TAG = Constants.LOGGER_TAG_NAME

    private val SENSITIVE_PATTERNS = listOf(
        "token", "password", "auth", "bearer", "session", "key", "secret",
        "credential", "pin", "otp", "biometric", "face", "fingerprint"
    )

    fun d(message: String) {
        if (IN_DEBUG) {
            Log.d(TAG, sanitizeMessage(message))
        }
    }

    fun e(message: String, throwable: Throwable? = null) {
        if (IN_DEBUG) {
            Log.e(TAG, sanitizeMessage(message), throwable)
        }
    }

    fun i(message: String) {
        if (IN_DEBUG) {
            Log.i(TAG, sanitizeMessage(message))
        }
    }

    fun w(message: String) {
        if (IN_DEBUG) {
            Log.w(TAG, sanitizeMessage(message))
        }
    }

    private fun sanitizeMessage(message: String): String {
        var sanitized = message
        SENSITIVE_PATTERNS.forEach { pattern ->
            val regex = Regex("($pattern[\\s]*[=:]?[\\s]*)[^\\s,}\\]]+", RegexOption.IGNORE_CASE)
            sanitized = sanitized.replace(regex) { matchResult ->
                "${matchResult.groupValues[1]}[REDACTED]"
            }
        }
        return sanitized
    }

    fun logSafeMessage(level: String, message: String) {
        if (IN_DEBUG) {
            when (level.lowercase()) {
                "d", "debug" -> Log.d(TAG, "[SAFE] $message")
                "i", "info" -> Log.i(TAG, "[SAFE] $message")
                "w", "warn" -> Log.w(TAG, "[SAFE] $message")
                "e", "error" -> Log.e(TAG, "[SAFE] $message")
            }
        }
    }
}
