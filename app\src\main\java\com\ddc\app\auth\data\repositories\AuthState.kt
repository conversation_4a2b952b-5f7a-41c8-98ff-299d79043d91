package com.ddc.app.auth.data.repositories

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class AuthState(
    val authToken: String = "",
    val isAuthenticated: Boolean = false,
    val applicantId: String = "",
    val documentId: String = "",
    val userFirstname: String = "",
    val userLastname: String = "",
    val sessionToken: String = "",
    val productionKey: String = "",
    val productionKeyText: String = "",
    val encryptionKey: String = "",
    val deviceKeyIdentifier: String = "",
    val systemReady: Boolean = false,
    val documentScanDone: Boolean = false,
    val livenessDone: Boolean = false
): Parcelable
